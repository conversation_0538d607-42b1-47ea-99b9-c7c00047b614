{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\guideList\\\\GuideMenuOptions.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useRef, useState } from 'react';\nimport PopupList from '../guideList/PopupList';\nimport StopScrapingButton from \"../../AI/StopScrapingButton\";\nimport { ProductToursicon, Tooltipsicon, Bannersicon, Checklisticon, Hotspoticon, Surveyicon, Announcementsicon } from '../../../assets/icons/icons';\nimport './GuideMenuOptions.css';\nimport useDrawerStore from '../../../store/drawerStore';\nimport { isScrapingActive, stopScraping, startScraping, getScrapedDataCount, loadScrapedDataFromStorage, exportScrapedDataToFile } from '../../../services/ScrapingService';\nimport { Tooltip } from '@mui/material';\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\nimport ModernChatWindow from '../../AIAgent/ModernChatWindow';\nimport CreateWithAIButton from '../../AIAgent/CreateWithAIButton';\nimport userSession from '../../../store/userSession';\nimport { AccountContext } from '../../login/AccountContext';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Guidemenu = ({\n  setStepData,\n  onGridItemClick,\n  isCollapsed,\n  toggleDrawer,\n  activeMenu,\n  setActiveMenu,\n  searchText,\n  setSearchText,\n  setisShowIcon,\n  setIsPopupOpen,\n  setIsLoggedIn,\n  setIsTourPopupOpen,\n  setIsDrawerClosed,\n  setShowBannerenduser,\n  currentAccountId\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const {\n    setSelectedTemplate,\n    setSelectedTemplateTour,\n    setSteps,\n    steps,\n    setTooltipCount,\n    tooltipCount,\n    SetGuideName,\n    setIsTooltipPopup,\n    HotspotGuideDetails,\n    setBannerPopup,\n    setElementSelected,\n    TooltipGuideDetails,\n    HotspotGuideDetailsNew,\n    setSelectedStepTypeHotspot,\n    selectedTemplate,\n    selectedTemplateTour,\n    activeMenu: drawerActiveMenu,\n    searchText: drawerSearchText,\n    setActiveMenu: setDrawerActiveMenu,\n    setSearchText: setDrawerSearchText,\n    isExtensionClosed,\n    setIsExtensionClosed\n  } = useDrawerStore(state => state);\n  useEffect(() => {\n    if (isExtensionClosed) {\n      setIsExtensionClosed(false);\n    }\n  }, []);\n  // State for AI chat window visibility\n  const [isAIChatOpen, setIsAIChatOpen] = useState(false);\n  // Get the setCurrentGuideId function from userSession\n  const {\n    setCurrentGuideId\n  } = userSession(state => state);\n  const {\n    hasAnnouncementOpened,\n    setHasAnnouncementOpened\n  } = userSession();\n  const hasTriggered = useRef(false);\n  useEffect(() => {\n    if (hasAnnouncementOpened && !hasTriggered.current) {\n      setDrawerActiveMenu(\"announcements\");\n      setDrawerSearchText(\"Announcement\");\n      hasTriggered.current = true;\n      // optionally reset the flag if you want it to trigger again next login\n      setHasAnnouncementOpened(false);\n    }\n  }, [hasAnnouncementOpened]);\n\n  // Add this effect to handle reopening\n  useEffect(() => {\n    // When extension is reopened (isExtensionClosed becomes false)\n    if (!isExtensionClosed && activeMenu) {\n      // Reopen the popup with the previously selected menu\n      setDrawerActiveMenu(activeMenu);\n      setDrawerSearchText(searchText);\n      setIsPopupOpen(true);\n    }\n  }, [isExtensionClosed, activeMenu, searchText]);\n  const menuItems = [{\n    id: \"announcements\",\n    name: \"Announcement\",\n    // Use English for logic\n    description: translate(\"Helps to communicate important updates, notifications, or messages.\", {\n      defaultValue: \"Helps to communicate important updates, notifications, or messages.\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `${isCollapsed ? \"qadpt-colsvg\" : \"\"}`,\n      dangerouslySetInnerHTML: {\n        __html: Announcementsicon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 10\n    }, this)\n  }, {\n    id: \"banners\",\n    name: \"Banner\",\n    description: translate(\"Displays notifications at the top or bottom of the screen.\", {\n      defaultValue: \"Displays notifications at the top or bottom of the screen.\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: Bannersicon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 10\n    }, this)\n  }, {\n    id: \"tooltips\",\n    name: \"Tooltip\",\n    description: translate(\"Provide quick explanations, tips, or instructions of the tools,\", {\n      defaultValue: \"Provide quick explanations, tips, or instructions of the tools,\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: Tooltipsicon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 10\n    }, this),\n    disabled: false\n  }, {\n    id: \"hotspot\",\n    name: \"Hotspot\",\n    description: translate(\"Interactive areas to draw attention to important features, actions, or guidance.\", {\n      defaultValue: \"Interactive areas to draw attention to important features, actions, or guidance.\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `${isCollapsed ? \"qadpt-colsvg\" : \"\"}`,\n      dangerouslySetInnerHTML: {\n        __html: Hotspoticon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 5\n    }, this),\n    disabled: false\n  }, {\n    id: \"tours\",\n    name: \"Tour\",\n    description: translate(\"Step-by-step guides to navigate and understand key features.\", {\n      defaultValue: \"Step-by-step guides to navigate and understand key features.\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: ProductToursicon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 10\n    }, this),\n    disabled: false\n  }, {\n    id: \"checklists\",\n    name: \"Checklist\",\n    description: translate(\"Task lists that guide users through a series of steps or actions\", {\n      defaultValue: \"Task lists that guide users through a series of steps or actions\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `${isCollapsed ? \"qadpt-colsvg\" : \"\"}`,\n      dangerouslySetInnerHTML: {\n        __html: Checklisticon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 5\n    }, this),\n    disabled: false\n  }, {\n    id: \"survey\",\n    name: \"Survey\",\n    description: translate(\"Interactive forms or questionnaires designed to collect feedback, insights, or opinions\", {\n      defaultValue: \"Interactive forms or questionnaires designed to collect feedback, insights, or opinions\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: Surveyicon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 10\n    }, this),\n    disabled: true\n  }];\n  // State for AI scraping\n  const [showScrapingButton, setShowScrapingButton] = useState(false);\n  const [scrapedCount, setScrapedCount] = useState(0);\n\n  // Check scraping status on component mount and periodically\n  useEffect(() => {\n    // Initial check\n    setShowScrapingButton(isScrapingActive());\n    setScrapedCount(getScrapedDataCount());\n\n    // Set up periodic check\n    const checkInterval = setInterval(() => {\n      setShowScrapingButton(isScrapingActive());\n      setScrapedCount(getScrapedDataCount());\n    }, 1000);\n\n    // Clean up interval on unmount\n    return () => clearInterval(checkInterval);\n  }, []);\n  const handleEnableAI = async () => {\n    // Check if scraping is already active\n    if (isScrapingActive()) {\n      alert('Scraping is already in progress');\n      return;\n    }\n    try {\n      // Start scraping directly without file upload\n      startScraping();\n      setShowScrapingButton(true);\n    } catch (error) {\n      console.error('Error starting scraping:', error);\n      alert('Error starting scraping');\n    }\n  };\n  const handleStopScraping = async () => {\n    try {\n      // Use the service to stop scraping\n      await stopScraping(accountId);\n      setShowScrapingButton(false);\n\n      // Show scraped data in console\n      if (scrapedCount > 0) {} else {\n        alert('No elements were scraped.');\n      }\n    } catch (error) {\n      console.error('Error stopping scraping:', error);\n      alert('Error stopping scraping');\n    }\n  };\n  const handleViewScrapedData = async () => {\n    try {\n      // Also try to load and display stored data\n      const storedData = await loadScrapedDataFromStorage();\n      if (storedData) {\n        console.group('🗄️ Stored Scraped Data');\n        console.log('Stored data:', storedData);\n        console.groupEnd();\n      }\n\n      // alert(`${scrapedCount} elements have been scraped. Check the console for current and stored data.`);\n    } catch (error) {\n      console.error('Error viewing scraped data:', error);\n      // alert(`${scrapedCount} elements have been scraped. Check the console for details.`);\n    }\n  };\n  const handleExportToFile = async () => {\n    try {\n      await exportScrapedDataToFile();\n    } catch (error) {\n      console.error('Error sending to API:', error);\n      alert('Error sending data to backend API. Check console for details.');\n    }\n  };\n  const handleMenuClick = async (menuId, menuName) => {\n    var _menuItems$find;\n    if (isCollapsed === true) {\n      toggleDrawer(false);\n    }\n    if ((_menuItems$find = menuItems.find(item => item.id === menuId)) !== null && _menuItems$find !== void 0 && _menuItems$find.disabled) return;\n\n    // Handle AI chat window separately\n    if (menuId === \"createwithai\") {\n      setIsAIChatOpen(true);\n      return;\n    }\n    setDrawerActiveMenu(menuId);\n    setDrawerSearchText(menuName);\n  };\n  const handleClosePopup = () => {\n    setDrawerActiveMenu(null);\n    setDrawerSearchText(\"\");\n  };\n  const handleCloseAIChat = () => {\n    setIsAIChatOpen(false);\n  };\n  const handleAddClick = (searchText, isEditing = false, guideDetails = null) => {\n    var _menuItems$find2;\n    setisShowIcon(true);\n    onGridItemClick(searchText, isEditing, guideDetails);\n    setDrawerActiveMenu(((_menuItems$find2 = menuItems.find(item => item.name === searchText)) === null || _menuItems$find2 === void 0 ? void 0 : _menuItems$find2.id) || null);\n    if (isCollapsed) {\n      toggleDrawer();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      top: isCollapsed ? \"20px\" : \"0px\",\n      position: \"relative\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"side-menu\",\n      style: {\n        height: \"calc(100vh - 65px)\",\n        overflow: \"hidden\"\n      },\n      children: /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n        children: [/*#__PURE__*/_jsxDEV(CreateWithAIButton, {\n          onClick: () => handleMenuClick(\"createwithai\", translate(\"Create With AI\"))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"menu-list\",\n          children: menuItems.map(item => /*#__PURE__*/_jsxDEV(Tooltip, {\n            arrow: true,\n            title: item.disabled ? translate(\"Coming Soon\") : translate(item.name),\n            PopperProps: {\n              sx: {\n                zIndex: 9999\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                cursor: item.disabled ? \"not-allowed\" : \"pointer\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"li\", {\n                \"data-id\": item.id,\n                className: `menu-item ${drawerActiveMenu === item.id ? \"active\" : \"\"} ${item.disabled ? \"disabled\" : \"\"}`,\n                onClick: () => handleMenuClick(item.id, item.name),\n                style: {\n                  opacity: item.disabled ? 0.5 : 1\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"menu-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"icons\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: item.icon\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 11\n                  }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"menu-text\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"menu-title\",\n                      children: item.id === \"createwithai\" ? translate(item.name) : translate(`${item.name}`, {\n                        defaultValue: `${item.name}s`\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 13\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"menu-description\",\n                      children: item.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 13\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 12\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 8\n            }, this)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 7\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 5\n        }, this), !isCollapsed && !isAIChatOpen && menuItems.map(item => /*#__PURE__*/_jsxDEV(PopupList, {\n          title: item.name,\n          Open: drawerActiveMenu === item.id,\n          onClose: handleClosePopup,\n          searchText: drawerSearchText,\n          onAddClick: handleAddClick\n        }, item.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 6\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 4\n    }, this), isAIChatOpen && /*#__PURE__*/_jsxDEV(ModernChatWindow, {\n      onClose: handleCloseAIChat,\n      setStepData: setStepData,\n      setIsAIChatOpen: setIsAIChatOpen,\n      setCurrentGuideId: setCurrentGuideId,\n      setIsPopupOpen: setIsPopupOpen,\n      setIsLoggedIn: setIsLoggedIn,\n      setIsTourPopupOpen: setIsTourPopupOpen,\n      setIsDrawerClosed: setIsDrawerClosed,\n      setShowBannerenduser: setShowBannerenduser\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 4\n    }, this), showScrapingButton && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(StopScrapingButton, {\n        onClick: handleStopScraping\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 5\n      }, this), scrapedCount > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#4CAF50',\n            textAlign: 'center',\n            cursor: 'pointer',\n            padding: '4px 8px',\n            backgroundColor: 'rgba(76, 175, 80, 0.1)',\n            borderRadius: '4px',\n            border: '1px solid rgba(76, 175, 80, 0.3)'\n          },\n          onClick: handleViewScrapedData,\n          children: [\"\\uD83D\\uDCCA \", scrapedCount, \" elements scraped (click to view)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            fontSize: '12px',\n            color: '#2196F3',\n            backgroundColor: 'rgba(33, 150, 243, 0.1)',\n            border: '1px solid rgba(33, 150, 243, 0.3)',\n            borderRadius: '4px',\n            padding: '6px 12px',\n            cursor: 'pointer',\n            fontWeight: 'bold'\n          },\n          onClick: handleExportToFile,\n          children: \"\\uD83D\\uDE80 Send to API\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 282,\n    columnNumber: 3\n  }, this);\n};\n_s(Guidemenu, \"Fr6nFbrimwKeGcW8XNJASZXgbnA=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = Guidemenu;\nexport default Guidemenu;\nvar _c;\n$RefreshReg$(_c, \"Guidemenu\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useRef", "useState", "PopupList", "StopScrapingButton", "ProductToursicon", "Tooltipsicon", "Bannersicon", "<PERSON><PERSON><PERSON>", "Hotspoticon", "Surveyicon", "Announcementsicon", "useDrawerStore", "isScrapingActive", "stopScraping", "startScraping", "getScrapedDataCount", "loadScrapedDataFromStorage", "exportScrapedDataToFile", "<PERSON><PERSON><PERSON>", "PerfectScrollbar", "ModernChatWindow", "CreateWithAIButton", "userSession", "AccountContext", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Guidemenu", "setStepData", "onGridItemClick", "isCollapsed", "toggle<PERSON>rawer", "activeMenu", "setActiveMenu", "searchText", "setSearchText", "setisShowIcon", "setIsPopupOpen", "setIsLoggedIn", "setIsTourPopupOpen", "setIsDrawerClosed", "setShowBannerenduser", "currentAccountId", "_s", "t", "translate", "accountId", "setSelectedTemplate", "setSelectedTemplateTour", "setSteps", "steps", "setTooltipCount", "tooltipCount", "SetGuideName", "setIsTooltipPopup", "HotspotGuideDetails", "setBannerPopup", "setElementSelected", "TooltipGuideDetails", "HotspotGuideDetailsNew", "setSelectedStepTypeHotspot", "selectedTemplate", "selectedTemplateTour", "drawerActiveMenu", "drawerSearchText", "setDrawerActiveMenu", "setDrawerSearchText", "isExtensionClosed", "setIsExtensionClosed", "state", "isAIChatOpen", "setIsAIChatOpen", "setCurrentGuideId", "hasAnnouncementOpened", "setHasAnnouncementOpened", "hasTriggered", "current", "menuItems", "id", "name", "description", "defaultValue", "icon", "className", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "showScrapingButton", "setShowScrapingButton", "scrapedCount", "setScrapedCount", "checkInterval", "setInterval", "clearInterval", "handleEnableAI", "alert", "error", "console", "handleStopScraping", "handleViewScrapedData", "storedData", "group", "log", "groupEnd", "handleExportToFile", "handleMenuClick", "menuId", "menuName", "_menuItems$find", "find", "item", "handleClosePopup", "handleCloseAIChat", "handleAddClick", "isEditing", "guideDetails", "_menuItems$find2", "style", "top", "position", "children", "height", "overflow", "onClick", "map", "arrow", "title", "PopperProps", "sx", "zIndex", "cursor", "opacity", "Open", "onClose", "onAddClick", "display", "flexDirection", "gap", "fontSize", "color", "textAlign", "padding", "backgroundColor", "borderRadius", "border", "fontWeight", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/GuideMenuOptions.tsx"], "sourcesContent": ["import React, { useContext, useEffect, useRef, useState } from 'react';\r\nimport PopupList from '../guideList/PopupList';\r\nimport StopScrapingButton from \"../../AI/StopScrapingButton\";\r\nimport {\r\n    ProductToursicon, Tooltipsicon,\r\n    announcementicon, Bannersicon,\r\n    Checklisticon, Hotspoticon,\r\n    Surveyicon, Announcementsicon,\r\n    ai\r\n} from '../../../assets/icons/icons';\r\nimport './GuideMenuOptions.css';\r\nimport EnableAIButton from '../../AI/EnableAI';\r\nimport useDrawerStore, { DrawerState } from '../../../store/drawerStore';\r\nimport { isScrapingActive, stopScraping, startScraping, getScrapedDataCount, loadScrapedDataFromStorage, exportScrapedDataToFile } from '../../../services/ScrapingService';\r\n\r\nimport { Tooltip } from '@mui/material';\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\nimport ModernChatWindow from '../../AIAgent/ModernChatWindow';\r\nimport CreateWithAIButton from '../../AIAgent/CreateWithAIButton';\r\n\r\nimport userSession from '../../../store/userSession';\r\nimport { AccountContext } from '../../login/AccountContext';\r\nimport { IsOpenAIKeyEnabledForAccount } from '../../../services/GuideListServices';\r\n\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n\r\n\r\nconst Guidemenu = ({\r\n\tsetStepData,\r\n\tonGridItemClick,\r\n\tisCollapsed,\r\n\ttoggleDrawer,\r\n\tactiveMenu,\r\n\tsetActiveMenu,\r\n\tsearchText,\r\n\tsetSearchText,\r\n\tsetisShowIcon,\r\n\tsetIsPopupOpen,\r\n\tsetIsLoggedIn,\r\n\tsetIsTourPopupOpen,\r\n\tsetIsDrawerClosed,\r\n\tsetShowBannerenduser,\r\n\tcurrentAccountId\r\n}: any) => {\r\n\tconst { t: translate } = useTranslation();\r\nconst{accountId}=useContext(AccountContext);\r\n\tconst {\r\n\t\tsetSelectedTemplate,\r\n\t\tsetSelectedTemplateTour,\r\n\t\tsetSteps,\r\n\t\tsteps,\r\n\t\tsetTooltipCount,\r\n\t\ttooltipCount,\r\n\t\tSetGuideName,\r\n\t\tsetIsTooltipPopup,\r\n\t\tHotspotGuideDetails,\r\n\t\tsetBannerPopup,\r\n\t\tsetElementSelected,\r\n\t\tTooltipGuideDetails,\r\n\t\tHotspotGuideDetailsNew,\r\n\t\tsetSelectedStepTypeHotspot,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tactiveMenu: drawerActiveMenu,\r\n\t\tsearchText: drawerSearchText,\r\n\t\tsetActiveMenu: setDrawerActiveMenu,\r\n\t\tsetSearchText: setDrawerSearchText,\r\n\t\tisExtensionClosed,\r\n\t\tsetIsExtensionClosed\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tuseEffect(() => {\r\n\t\tif (isExtensionClosed) {\r\n\t\t\tsetIsExtensionClosed(false);\r\n\t\t}\r\n\t}, []);\r\n\t// State for AI chat window visibility\r\n\tconst [isAIChatOpen, setIsAIChatOpen] = useState(false);\r\n\t// Get the setCurrentGuideId function from userSession\r\n\tconst { setCurrentGuideId } = userSession((state: any) => state);\r\n\r\n\r\n\tconst { hasAnnouncementOpened, setHasAnnouncementOpened } = userSession();\r\n\tconst hasTriggered = useRef(false);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (hasAnnouncementOpened && !hasTriggered.current) {\r\n\t\t\tsetDrawerActiveMenu(\"announcements\");\r\n\t\t\tsetDrawerSearchText(\"Announcement\");\r\n\t\t\thasTriggered.current = true;\r\n\t\t\t// optionally reset the flag if you want it to trigger again next login\r\n\t\t\tsetHasAnnouncementOpened(false);\r\n\t\t}\r\n\t}, [hasAnnouncementOpened]);\r\n\r\n\t// Add this effect to handle reopening\r\n\tuseEffect(() => {\r\n\t\t// When extension is reopened (isExtensionClosed becomes false)\r\n\t\tif (!isExtensionClosed && activeMenu) {\r\n\t\t\t// Reopen the popup with the previously selected menu\r\n\t\t\tsetDrawerActiveMenu(activeMenu);\r\n\t\t\tsetDrawerSearchText(searchText);\r\n\t\t\tsetIsPopupOpen(true);\r\n\t\t}\r\n\t}, [isExtensionClosed, activeMenu, searchText]);\r\n\tconst menuItems = [\r\n\t\t{\r\n\t\t\tid: \"announcements\",\r\n\t\t\tname: \"Announcement\", // Use English for logic\r\n\t\t\tdescription: translate(\"Helps to communicate important updates, notifications, or messages.\", { defaultValue: \"Helps to communicate important updates, notifications, or messages.\" }),\r\n\t\t\ticon: <span className={`${isCollapsed ? \"qadpt-colsvg\" : \"\"}`}\r\n\t\t\tdangerouslySetInnerHTML={{ __html: Announcementsicon }} />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"banners\",\r\n\t\t\tname: \"Banner\",\r\n\t\t\tdescription: translate(\"Displays notifications at the top or bottom of the screen.\", { defaultValue: \"Displays notifications at the top or bottom of the screen.\" }),\r\n\t\t\ticon: <span dangerouslySetInnerHTML={{ __html: Bannersicon }} />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"tooltips\",\r\n\t\t\tname: \"Tooltip\",\r\n\t\t\tdescription: translate(\"Provide quick explanations, tips, or instructions of the tools,\", { defaultValue: \"Provide quick explanations, tips, or instructions of the tools,\" }),\r\n\t\t\ticon: <span dangerouslySetInnerHTML={{ __html: Tooltipsicon }} />,\r\n\t\t\tdisabled: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"hotspot\",\r\n\t\t\tname: \"Hotspot\",\r\n\t\t\tdescription: translate(\"Interactive areas to draw attention to important features, actions, or guidance.\", { defaultValue: \"Interactive areas to draw attention to important features, actions, or guidance.\" }),\r\n\t\t\ticon: (\r\n\t\t\t\t<span\r\n\t\t\t\tclassName={`${isCollapsed ? \"qadpt-colsvg\" : \"\"}`}\r\n\t\t\t\tdangerouslySetInnerHTML={{ __html: Hotspoticon }}\r\n\t\t\t\t/>\r\n\t\t\t),\t\t\tdisabled: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"tours\",\r\n\t\t\tname: \"Tour\",\r\n\t\t\tdescription: translate(\"Step-by-step guides to navigate and understand key features.\", { defaultValue: \"Step-by-step guides to navigate and understand key features.\" }),\r\n\t\t\ticon: <span dangerouslySetInnerHTML={{ __html: ProductToursicon }} />,\r\n\t\t\tdisabled: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"checklists\",\r\n\t\t\tname: \"Checklist\",\r\n\t\t\tdescription: translate(\"Task lists that guide users through a series of steps or actions\", { defaultValue: \"Task lists that guide users through a series of steps or actions\" }),\r\n\t\t\ticon: (\r\n\t\t\t\t<span\r\n\t\t\t\t\tclassName={`${isCollapsed ? \"qadpt-colsvg\" : \"\"}`}\r\n\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Checklisticon }}\r\n\t\t\t\t/>\r\n\t\t\t),\t\t\tdisabled: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"survey\",\r\n\t\t\tname: \"Survey\",\r\n\t\t\tdescription: translate(\"Interactive forms or questionnaires designed to collect feedback, insights, or opinions\", { defaultValue: \"Interactive forms or questionnaires designed to collect feedback, insights, or opinions\" }),\r\n\t\t\ticon: <span dangerouslySetInnerHTML={{ __html: Surveyicon }} />,\r\n\t\t\tdisabled: true,\r\n\t\t},\r\n\t];\r\n\t// State for AI scraping\r\n    const [showScrapingButton, setShowScrapingButton] = useState(false);\r\n    const [scrapedCount, setScrapedCount] = useState(0);\r\n\r\n    // Check scraping status on component mount and periodically\r\n    useEffect(() => {\r\n        // Initial check\r\n        setShowScrapingButton(isScrapingActive());\r\n        setScrapedCount(getScrapedDataCount());\r\n\r\n        // Set up periodic check\r\n        const checkInterval = setInterval(() => {\r\n            setShowScrapingButton(isScrapingActive());\r\n            setScrapedCount(getScrapedDataCount());\r\n        }, 1000);\r\n\r\n        // Clean up interval on unmount\r\n        return () => clearInterval(checkInterval);\r\n    }, []);\r\n\r\n\tconst handleEnableAI = async () => {\r\n        // Check if scraping is already active\r\n        if (isScrapingActive()) {\r\n            alert('Scraping is already in progress');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            // Start scraping directly without file upload\r\n            startScraping();\r\n            setShowScrapingButton(true);\r\n        } catch (error) {\r\n            console.error('Error starting scraping:', error);\r\n            alert('Error starting scraping');\r\n        }\r\n    };\r\n\r\n    const handleStopScraping = async () => {\r\n        try {\r\n            // Use the service to stop scraping\r\n            await stopScraping(accountId);\r\n            setShowScrapingButton(false);\r\n\r\n            // Show scraped data in console\r\n            if (scrapedCount > 0) {\r\n            } else {\r\n                alert('No elements were scraped.');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error stopping scraping:', error);\r\n            alert('Error stopping scraping');\r\n        }\r\n    };\r\n\r\n    const handleViewScrapedData = async () => {\r\n        try {\r\n            \r\n\r\n            // Also try to load and display stored data\r\n            const storedData = await loadScrapedDataFromStorage();\r\n            if (storedData) {\r\n                console.group('🗄️ Stored Scraped Data');\r\n                console.log('Stored data:', storedData);\r\n                console.groupEnd();\r\n            }\r\n\r\n           // alert(`${scrapedCount} elements have been scraped. Check the console for current and stored data.`);\r\n        } catch (error) {\r\n            console.error('Error viewing scraped data:', error);\r\n           // alert(`${scrapedCount} elements have been scraped. Check the console for details.`);\r\n        }\r\n    };\r\n\r\n    const handleExportToFile = async () => {\r\n        try {\r\n            await exportScrapedDataToFile();\r\n        } catch (error) {\r\n            console.error('Error sending to API:', error);\r\n            alert('Error sending data to backend API. Check console for details.');\r\n        }\r\n    };\r\n\r\n\tconst handleMenuClick = async (menuId: string, menuName: string) => {\r\n\t\tif (isCollapsed === true) {\r\n\t\t\ttoggleDrawer(false);\r\n\t\t}\r\n\t\tif (menuItems.find((item) => item.id === menuId)?.disabled) return;\r\n\r\n\t\t// Handle AI chat window separately\r\n\t\tif (menuId === \"createwithai\") {\r\n\t\t\tsetIsAIChatOpen(true);\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tsetDrawerActiveMenu(menuId);\r\n\t\tsetDrawerSearchText(menuName);\r\n\t};\r\n\r\n\tconst handleClosePopup = () => {\r\n\t\tsetDrawerActiveMenu(null);\r\n\t\tsetDrawerSearchText(\"\");\r\n\t};\r\n\r\n\tconst handleCloseAIChat = () => {\r\n\t\tsetIsAIChatOpen(false);\r\n\t};\r\n\r\n\tconst handleAddClick = (searchText: string, isEditing: boolean = false, guideDetails: any = null) => {\r\n\t\tsetisShowIcon(true);\r\n\t\tonGridItemClick(searchText, isEditing, guideDetails);\r\n\t\tsetDrawerActiveMenu(menuItems.find((item) => item.name === searchText)?.id || null);\r\n\t\tif (isCollapsed) {\r\n\t\t\ttoggleDrawer();\r\n\t\t}\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div style={{ top: isCollapsed ? \"20px\" : \"0px\", position: \"relative\" }}>\r\n\r\n\t\t\t<div className=\"side-menu\" style={{ height: \"calc(100vh - 65px)\", overflow: \"hidden\" }}>\r\n\t\t\t<PerfectScrollbar>\r\n\t\t\t\t\t<CreateWithAIButton onClick={() => handleMenuClick(\"createwithai\", translate(\"Create With AI\"))} />\r\n\t\t\t\t<ul className=\"menu-list\">\r\n\t\t\t\t\t{menuItems.map((item) => (\r\n\t\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\ttitle={item.disabled ? translate(\"Coming Soon\") : translate(item.name)}\r\n\t\t\t\t\t\t\tPopperProps={{ sx: { zIndex: 9999 } }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ cursor: item.disabled ? \"not-allowed\" : \"pointer\" }}>\r\n\t\t\t\t\t\t\t\t<li\r\n\t\t\t\t\t\t\t\t\tdata-id={item.id}\r\n\t\t\t\t\t\t\t\t\tclassName={`menu-item ${drawerActiveMenu === item.id ? \"active\" : \"\"} ${item.disabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleMenuClick(item.id, item.name)}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: item.disabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"menu-content\">\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"icons\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div>{item.icon}</div>\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t{!isCollapsed && (\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"menu-text\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"menu-title\">{item.id === \"createwithai\" ? translate(item.name) : translate(`${item.name}`, { defaultValue: `${item.name}s` })}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"menu-description\">{item.description}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</ul>\r\n\t\t\t\t{/* Render popups for each menu item */}\r\n\t\t\t\t{!isCollapsed && !isAIChatOpen && menuItems.map((item) => (\r\n\t\t\t\t\t<PopupList\r\n\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\ttitle={item.name}\r\n\t\t\t\t\t\tOpen={drawerActiveMenu === item.id}\r\n\t\t\t\t\t\tonClose={handleClosePopup}\r\n\t\t\t\t\t\tsearchText={drawerSearchText}\r\n\t\t\t\t\t\tonAddClick={handleAddClick}\r\n\t\t\t\t\t/>\r\n\t\t\t\t))}\r\n\t\t\t</PerfectScrollbar>\r\n\t\t</div>\r\n\t\t{/* AI Chat Window */}\r\n\t\t{isAIChatOpen && (\r\n\t\t\t<ModernChatWindow\r\n\t\t\t\tonClose={handleCloseAIChat}\r\n\t\t\t\tsetStepData={setStepData}\r\n\t\t\t\tsetIsAIChatOpen={setIsAIChatOpen}\r\n\t\t\t\tsetCurrentGuideId={setCurrentGuideId}\r\n\t\t\t\tsetIsPopupOpen={setIsPopupOpen}\r\n\t\t\t\tsetIsLoggedIn={setIsLoggedIn}\r\n\t\t\t\tsetIsTourPopupOpen={setIsTourPopupOpen}\r\n\t\t\t\tsetIsDrawerClosed={setIsDrawerClosed}\r\n\t\t\t\tsetShowBannerenduser={setShowBannerenduser}\r\n\t\t\t/>\r\n\t\t)}\r\n\t\t{/* <EnableAIButton onClick={handleEnableAI} /> */}\r\n\t\t{showScrapingButton && (\r\n\t\t\t<div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\r\n\t\t\t\t<StopScrapingButton onClick={handleStopScraping} />\r\n\t\t\t\t{scrapedCount > 0 && (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\tfontSize: '12px',\r\n\t\t\t\t\t\t\tcolor: '#4CAF50',\r\n\t\t\t\t\t\t\ttextAlign: 'center',\r\n\t\t\t\t\t\t\tcursor: 'pointer',\r\n\t\t\t\t\t\t\tpadding: '4px 8px',\r\n\t\t\t\t\t\t\tbackgroundColor: 'rgba(76, 175, 80, 0.1)',\r\n\t\t\t\t\t\t\tborderRadius: '4px',\r\n\t\t\t\t\t\t\tborder: '1px solid rgba(76, 175, 80, 0.3)'\r\n\t\t\t\t\t\t}} onClick={handleViewScrapedData}>\r\n\t\t\t\t\t\t\t📊 {scrapedCount} elements scraped (click to view)\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<button style={{\r\n\t\t\t\t\t\t\tfontSize: '12px',\r\n\t\t\t\t\t\t\tcolor: '#2196F3',\r\n\t\t\t\t\t\t\tbackgroundColor: 'rgba(33, 150, 243, 0.1)',\r\n\t\t\t\t\t\t\tborder: '1px solid rgba(33, 150, 243, 0.3)',\r\n\t\t\t\t\t\t\tborderRadius: '4px',\r\n\t\t\t\t\t\t\tpadding: '6px 12px',\r\n\t\t\t\t\t\t\tcursor: 'pointer',\r\n\t\t\t\t\t\t\tfontWeight: 'bold'\r\n\t\t\t\t\t\t}} onClick={handleExportToFile}>\r\n\t\t\t\t\t\t\t🚀 Send to API\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t)}\r\n\t</div>\r\n);\r\n}\r\n\r\nexport default Guidemenu;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACtE,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,SACIC,gBAAgB,EAAEC,YAAY,EACZC,WAAW,EAC7BC,aAAa,EAAEC,WAAW,EAC1BC,UAAU,EAAEC,iBAAiB,QAE1B,6BAA6B;AACpC,OAAO,wBAAwB;AAE/B,OAAOC,cAAc,MAAuB,4BAA4B;AACxE,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,uBAAuB,QAAQ,mCAAmC;AAE3K,SAASC,OAAO,QAAQ,eAAe;AACvC,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AAEpD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,kBAAkB,MAAM,kCAAkC;AAEjE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,cAAc,QAAQ,4BAA4B;AAG3D,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI/C,MAAMC,SAAS,GAAGA,CAAC;EAClBC,WAAW;EACXC,eAAe;EACfC,WAAW;EACXC,YAAY;EACZC,UAAU;EACVC,aAAa;EACbC,UAAU;EACVC,aAAa;EACbC,aAAa;EACbC,cAAc;EACdC,aAAa;EACbC,kBAAkB;EAClBC,iBAAiB;EACjBC,oBAAoB;EACpBC;AACI,CAAC,KAAK;EAAAC,EAAA;EACV,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGvB,cAAc,CAAC,CAAC;EAC1C,MAAK;IAACwB;EAAS,CAAC,GAAClD,UAAU,CAACyB,cAAc,CAAC;EAC1C,MAAM;IACL0B,mBAAmB;IACnBC,uBAAuB;IACvBC,QAAQ;IACRC,KAAK;IACLC,eAAe;IACfC,YAAY;IACZC,YAAY;IACZC,iBAAiB;IACjBC,mBAAmB;IACnBC,cAAc;IACdC,kBAAkB;IAClBC,mBAAmB;IACnBC,sBAAsB;IACtBC,0BAA0B;IAC1BC,gBAAgB;IAChBC,oBAAoB;IACpB9B,UAAU,EAAE+B,gBAAgB;IAC5B7B,UAAU,EAAE8B,gBAAgB;IAC5B/B,aAAa,EAAEgC,mBAAmB;IAClC9B,aAAa,EAAE+B,mBAAmB;IAClCC,iBAAiB;IACjBC;EACD,CAAC,GAAG3D,cAAc,CAAE4D,KAAkB,IAAKA,KAAK,CAAC;EACjDxE,SAAS,CAAC,MAAM;IACf,IAAIsE,iBAAiB,EAAE;MACtBC,oBAAoB,CAAC,KAAK,CAAC;IAC5B;EACD,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACvD;EACA,MAAM;IAAEyE;EAAkB,CAAC,GAAGpD,WAAW,CAAEiD,KAAU,IAAKA,KAAK,CAAC;EAGhE,MAAM;IAAEI,qBAAqB;IAAEC;EAAyB,CAAC,GAAGtD,WAAW,CAAC,CAAC;EACzE,MAAMuD,YAAY,GAAG7E,MAAM,CAAC,KAAK,CAAC;EAElCD,SAAS,CAAC,MAAM;IACf,IAAI4E,qBAAqB,IAAI,CAACE,YAAY,CAACC,OAAO,EAAE;MACnDX,mBAAmB,CAAC,eAAe,CAAC;MACpCC,mBAAmB,CAAC,cAAc,CAAC;MACnCS,YAAY,CAACC,OAAO,GAAG,IAAI;MAC3B;MACAF,wBAAwB,CAAC,KAAK,CAAC;IAChC;EACD,CAAC,EAAE,CAACD,qBAAqB,CAAC,CAAC;;EAE3B;EACA5E,SAAS,CAAC,MAAM;IACf;IACA,IAAI,CAACsE,iBAAiB,IAAInC,UAAU,EAAE;MACrC;MACAiC,mBAAmB,CAACjC,UAAU,CAAC;MAC/BkC,mBAAmB,CAAChC,UAAU,CAAC;MAC/BG,cAAc,CAAC,IAAI,CAAC;IACrB;EACD,CAAC,EAAE,CAAC8B,iBAAiB,EAAEnC,UAAU,EAAEE,UAAU,CAAC,CAAC;EAC/C,MAAM2C,SAAS,GAAG,CACjB;IACCC,EAAE,EAAE,eAAe;IACnBC,IAAI,EAAE,cAAc;IAAE;IACtBC,WAAW,EAAEnC,SAAS,CAAC,qEAAqE,EAAE;MAAEoC,YAAY,EAAE;IAAsE,CAAC,CAAC;IACtLC,IAAI,eAAE1D,OAAA;MAAM2D,SAAS,EAAE,GAAGrD,WAAW,GAAG,cAAc,GAAG,EAAE,EAAG;MAC9DsD,uBAAuB,EAAE;QAAEC,MAAM,EAAE7E;MAAkB;IAAE;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC1D,CAAC,EACD;IACCX,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAEnC,SAAS,CAAC,4DAA4D,EAAE;MAAEoC,YAAY,EAAE;IAA6D,CAAC,CAAC;IACpKC,IAAI,eAAE1D,OAAA;MAAM4D,uBAAuB,EAAE;QAAEC,MAAM,EAAEjF;MAAY;IAAE;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAChE,CAAC,EACD;IACCX,EAAE,EAAE,UAAU;IACdC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAEnC,SAAS,CAAC,iEAAiE,EAAE;MAAEoC,YAAY,EAAE;IAAkE,CAAC,CAAC;IAC9KC,IAAI,eAAE1D,OAAA;MAAM4D,uBAAuB,EAAE;QAAEC,MAAM,EAAElF;MAAa;IAAE;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjEC,QAAQ,EAAE;EACX,CAAC,EACD;IACCZ,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAEnC,SAAS,CAAC,kFAAkF,EAAE;MAAEoC,YAAY,EAAE;IAAmF,CAAC,CAAC;IAChNC,IAAI,eACH1D,OAAA;MACA2D,SAAS,EAAE,GAAGrD,WAAW,GAAG,cAAc,GAAG,EAAE,EAAG;MAClDsD,uBAAuB,EAAE;QAAEC,MAAM,EAAE/E;MAAY;IAAE;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACD;IAAIC,QAAQ,EAAE;EAChB,CAAC,EACD;IACCZ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAEnC,SAAS,CAAC,8DAA8D,EAAE;MAAEoC,YAAY,EAAE;IAA+D,CAAC,CAAC;IACxKC,IAAI,eAAE1D,OAAA;MAAM4D,uBAAuB,EAAE;QAAEC,MAAM,EAAEnF;MAAiB;IAAE;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrEC,QAAQ,EAAE;EACX,CAAC,EACD;IACCZ,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAEnC,SAAS,CAAC,kEAAkE,EAAE;MAAEoC,YAAY,EAAE;IAAmE,CAAC,CAAC;IAChLC,IAAI,eACH1D,OAAA;MACC2D,SAAS,EAAE,GAAGrD,WAAW,GAAG,cAAc,GAAG,EAAE,EAAG;MAClDsD,uBAAuB,EAAE;QAAEC,MAAM,EAAEhF;MAAc;IAAE;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CACD;IAAIC,QAAQ,EAAE;EAChB,CAAC,EACD;IACCZ,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAEnC,SAAS,CAAC,yFAAyF,EAAE;MAAEoC,YAAY,EAAE;IAA0F,CAAC,CAAC;IAC9NC,IAAI,eAAE1D,OAAA;MAAM4D,uBAAuB,EAAE;QAAEC,MAAM,EAAE9E;MAAW;IAAE;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/DC,QAAQ,EAAE;EACX,CAAC,CACD;EACD;EACG,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACAF,SAAS,CAAC,MAAM;IACZ;IACA+F,qBAAqB,CAAClF,gBAAgB,CAAC,CAAC,CAAC;IACzCoF,eAAe,CAACjF,mBAAmB,CAAC,CAAC,CAAC;;IAEtC;IACA,MAAMkF,aAAa,GAAGC,WAAW,CAAC,MAAM;MACpCJ,qBAAqB,CAAClF,gBAAgB,CAAC,CAAC,CAAC;MACzCoF,eAAe,CAACjF,mBAAmB,CAAC,CAAC,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,OAAO,MAAMoF,aAAa,CAACF,aAAa,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;EAET,MAAMG,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC5B;IACA,IAAIxF,gBAAgB,CAAC,CAAC,EAAE;MACpByF,KAAK,CAAC,iCAAiC,CAAC;MACxC;IACJ;IAEA,IAAI;MACA;MACAvF,aAAa,CAAC,CAAC;MACfgF,qBAAqB,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDD,KAAK,CAAC,yBAAyB,CAAC;IACpC;EACJ,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACA;MACA,MAAM3F,YAAY,CAACmC,SAAS,CAAC;MAC7B8C,qBAAqB,CAAC,KAAK,CAAC;;MAE5B;MACA,IAAIC,YAAY,GAAG,CAAC,EAAE,CACtB,CAAC,MAAM;QACHM,KAAK,CAAC,2BAA2B,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDD,KAAK,CAAC,yBAAyB,CAAC;IACpC;EACJ,CAAC;EAED,MAAMI,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MAGA;MACA,MAAMC,UAAU,GAAG,MAAM1F,0BAA0B,CAAC,CAAC;MACrD,IAAI0F,UAAU,EAAE;QACZH,OAAO,CAACI,KAAK,CAAC,yBAAyB,CAAC;QACxCJ,OAAO,CAACK,GAAG,CAAC,cAAc,EAAEF,UAAU,CAAC;QACvCH,OAAO,CAACM,QAAQ,CAAC,CAAC;MACtB;;MAED;IACH,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACpD;IACH;EACJ,CAAC;EAED,MAAMQ,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACA,MAAM7F,uBAAuB,CAAC,CAAC;IACnC,CAAC,CAAC,OAAOqF,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,KAAK,CAAC,+DAA+D,CAAC;IAC1E;EACJ,CAAC;EAEJ,MAAMU,eAAe,GAAG,MAAAA,CAAOC,MAAc,EAAEC,QAAgB,KAAK;IAAA,IAAAC,eAAA;IACnE,IAAIlF,WAAW,KAAK,IAAI,EAAE;MACzBC,YAAY,CAAC,KAAK,CAAC;IACpB;IACA,KAAAiF,eAAA,GAAInC,SAAS,CAACoC,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACpC,EAAE,KAAKgC,MAAM,CAAC,cAAAE,eAAA,eAA5CA,eAAA,CAA8CtB,QAAQ,EAAE;;IAE5D;IACA,IAAIoB,MAAM,KAAK,cAAc,EAAE;MAC9BvC,eAAe,CAAC,IAAI,CAAC;MACrB;IACD;IACAN,mBAAmB,CAAC6C,MAAM,CAAC;IAC3B5C,mBAAmB,CAAC6C,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC9BlD,mBAAmB,CAAC,IAAI,CAAC;IACzBC,mBAAmB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMkD,iBAAiB,GAAGA,CAAA,KAAM;IAC/B7C,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAM8C,cAAc,GAAGA,CAACnF,UAAkB,EAAEoF,SAAkB,GAAG,KAAK,EAAEC,YAAiB,GAAG,IAAI,KAAK;IAAA,IAAAC,gBAAA;IACpGpF,aAAa,CAAC,IAAI,CAAC;IACnBP,eAAe,CAACK,UAAU,EAAEoF,SAAS,EAAEC,YAAY,CAAC;IACpDtD,mBAAmB,CAAC,EAAAuD,gBAAA,GAAA3C,SAAS,CAACoC,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACnC,IAAI,KAAK7C,UAAU,CAAC,cAAAsF,gBAAA,uBAAlDA,gBAAA,CAAoD1C,EAAE,KAAI,IAAI,CAAC;IACnF,IAAIhD,WAAW,EAAE;MAChBC,YAAY,CAAC,CAAC;IACf;EACD,CAAC;EAED,oBACCP,OAAA;IAAKiG,KAAK,EAAE;MAAEC,GAAG,EAAE5F,WAAW,GAAG,MAAM,GAAG,KAAK;MAAE6F,QAAQ,EAAE;IAAW,CAAE;IAAAC,QAAA,gBAEvEpG,OAAA;MAAK2D,SAAS,EAAC,WAAW;MAACsC,KAAK,EAAE;QAAEI,MAAM,EAAE,oBAAoB;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAAF,QAAA,eACvFpG,OAAA,CAACP,gBAAgB;QAAA2G,QAAA,gBACfpG,OAAA,CAACL,kBAAkB;UAAC4G,OAAO,EAAEA,CAAA,KAAMlB,eAAe,CAAC,cAAc,EAAEhE,SAAS,CAAC,gBAAgB,CAAC;QAAE;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpGjE,OAAA;UAAI2D,SAAS,EAAC,WAAW;UAAAyC,QAAA,EACvB/C,SAAS,CAACmD,GAAG,CAAEd,IAAI,iBACnB1F,OAAA,CAACR,OAAO;YAACiH,KAAK;YAEbC,KAAK,EAAEhB,IAAI,CAACxB,QAAQ,GAAG7C,SAAS,CAAC,aAAa,CAAC,GAAGA,SAAS,CAACqE,IAAI,CAACnC,IAAI,CAAE;YACvEoD,WAAW,EAAE;cAAEC,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE,CAAE;YAAAT,QAAA,eAEtCpG,OAAA;cAAMiG,KAAK,EAAE;gBAAEa,MAAM,EAAEpB,IAAI,CAACxB,QAAQ,GAAG,aAAa,GAAG;cAAU,CAAE;cAAAkC,QAAA,eAClEpG,OAAA;gBACC,WAAS0F,IAAI,CAACpC,EAAG;gBACjBK,SAAS,EAAE,aAAapB,gBAAgB,KAAKmD,IAAI,CAACpC,EAAE,GAAG,QAAQ,GAAG,EAAE,IAAIoC,IAAI,CAACxB,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;gBAC1GqC,OAAO,EAAEA,CAAA,KAAMlB,eAAe,CAACK,IAAI,CAACpC,EAAE,EAAEoC,IAAI,CAACnC,IAAI,CAAE;gBACnD0C,KAAK,EAAE;kBAAEc,OAAO,EAAErB,IAAI,CAACxB,QAAQ,GAAG,GAAG,GAAG;gBAAE,CAAE;gBAAAkC,QAAA,eAE5CpG,OAAA;kBAAK2D,SAAS,EAAC,cAAc;kBAAAyC,QAAA,gBAC5BpG,OAAA;oBAAM2D,SAAS,EAAC,OAAO;oBAAAyC,QAAA,eACtBpG,OAAA;sBAAAoG,QAAA,EAAMV,IAAI,CAAChC;oBAAI;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,EACN,CAAC3D,WAAW,iBACZN,OAAA;oBAAK2D,SAAS,EAAC,WAAW;oBAAAyC,QAAA,gBACzBpG,OAAA;sBAAK2D,SAAS,EAAC,YAAY;sBAAAyC,QAAA,EAAEV,IAAI,CAACpC,EAAE,KAAK,cAAc,GAAGjC,SAAS,CAACqE,IAAI,CAACnC,IAAI,CAAC,GAAGlC,SAAS,CAAC,GAAGqE,IAAI,CAACnC,IAAI,EAAE,EAAE;wBAAEE,YAAY,EAAE,GAAGiC,IAAI,CAACnC,IAAI;sBAAI,CAAC;oBAAC;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpJjE,OAAA;sBAAK2D,SAAS,EAAC,kBAAkB;sBAAAyC,QAAA,EAAEV,IAAI,CAAClC;oBAAW;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CACL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC,GAvBFyB,IAAI,CAACpC,EAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEJ,CAAC3D,WAAW,IAAI,CAACwC,YAAY,IAAIO,SAAS,CAACmD,GAAG,CAAEd,IAAI,iBACpD1F,OAAA,CAACxB,SAAS;UAETkI,KAAK,EAAEhB,IAAI,CAACnC,IAAK;UACjByD,IAAI,EAAEzE,gBAAgB,KAAKmD,IAAI,CAACpC,EAAG;UACnC2D,OAAO,EAAEtB,gBAAiB;UAC1BjF,UAAU,EAAE8B,gBAAiB;UAC7B0E,UAAU,EAAErB;QAAe,GALtBH,IAAI,CAACpC,EAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMZ,CACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACe;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,EAELnB,YAAY,iBACZ9C,OAAA,CAACN,gBAAgB;MAChBuH,OAAO,EAAErB,iBAAkB;MAC3BxF,WAAW,EAAEA,WAAY;MACzB2C,eAAe,EAAEA,eAAgB;MACjCC,iBAAiB,EAAEA,iBAAkB;MACrCnC,cAAc,EAAEA,cAAe;MAC/BC,aAAa,EAAEA,aAAc;MAC7BC,kBAAkB,EAAEA,kBAAmB;MACvCC,iBAAiB,EAAEA,iBAAkB;MACrCC,oBAAoB,EAAEA;IAAqB;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CACD,EAEAE,kBAAkB,iBAClBnE,OAAA;MAAKiG,KAAK,EAAE;QAAEkB,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAjB,QAAA,gBACpEpG,OAAA,CAACvB,kBAAkB;QAAC8H,OAAO,EAAEzB;MAAmB;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAClDI,YAAY,GAAG,CAAC,iBAChBrE,OAAA,CAAAE,SAAA;QAAAkG,QAAA,gBACCpG,OAAA;UAAKiG,KAAK,EAAE;YACXqB,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,SAAS;YAChBC,SAAS,EAAE,QAAQ;YACnBV,MAAM,EAAE,SAAS;YACjBW,OAAO,EAAE,SAAS;YAClBC,eAAe,EAAE,wBAAwB;YACzCC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE;UACT,CAAE;UAACrB,OAAO,EAAExB,qBAAsB;UAAAqB,QAAA,GAAC,eAC/B,EAAC/B,YAAY,EAAC,mCAClB;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNjE,OAAA;UAAQiG,KAAK,EAAE;YACdqB,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,SAAS;YAChBG,eAAe,EAAE,yBAAyB;YAC1CE,MAAM,EAAE,mCAAmC;YAC3CD,YAAY,EAAE,KAAK;YACnBF,OAAO,EAAE,UAAU;YACnBX,MAAM,EAAE,SAAS;YACjBe,UAAU,EAAE;UACb,CAAE;UAACtB,OAAO,EAAEnB,kBAAmB;UAAAgB,QAAA,EAAC;QAEhC;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACR,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACL;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEP,CAAC;AAAA9C,EAAA,CA7VKhB,SAAS;EAAA,QAiBWL,cAAc,EAyBnCb,cAAc;AAAA;AAAA6I,EAAA,GA1Cb3H,SAAS;AA+Vf,eAAeA,SAAS;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}