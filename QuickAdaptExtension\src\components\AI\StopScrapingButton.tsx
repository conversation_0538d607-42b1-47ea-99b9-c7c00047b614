import React, { useContext } from 'react';
import { stopScraping } from '../../services/ScrapingService';
import './EnableAIButton.css';
import { AccountContext } from '../../components/login/AccountContext';

interface StopScrapingButtonProps {
  onClick: () => void;
}

const StopScrapingButton: React.FC<StopScrapingButtonProps> = ({ onClick }) => {
  const { accountId } = useContext(AccountContext);

  const handleClick = async () => {
    
    stopScraping(accountId);
    onClick();
  };
  
  ;
  
  

  return (
    <div className='stop-scraping-button-container' id='stop-scraping-button'>
      <button className="enable-ai-button stop-scraping-button" onClick={handleClick}>
        <span className="enable-ai-text">Stop Training</span>
      </button>
    </div>
  );
};

export default StopScrapingButton;