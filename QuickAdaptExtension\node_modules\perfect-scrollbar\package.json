{"name": "perfect-scrollbar", "version": "1.5.6", "description": "Minimalistic but perfect custom scrollbar plugin", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "keywords": ["frontend", "scroll", "scrollbar"], "homepage": "https://perfectscrollbar.com/", "repository": {"type": "git", "url": "https://github.com/mdbootstrap/perfect-scrollbar"}, "bugs": {"url": "https://github.com/mdbootstrap/perfect-scrollbar/issues"}, "jspm": {"main": "dist/perfect-scrollbar.common.js", "registry": "jspm"}, "main": "dist/perfect-scrollbar.common.js", "module": "dist/perfect-scrollbar.esm.js", "typings": "types/perfect-scrollbar.d.ts", "style": "css/perfect-scrollbar.css", "files": ["css", "dist", "src", "types"], "devDependencies": {"chokidar-cli": "^2.1.0", "prettier": "^1.19.1", "rollup": "^1.27.8", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-babel-minify": "^9.1.1", "rollup-plugin-buble": "^0.19.8"}, "scripts": {"test": "npm run format:lint && npm run build", "rollup": "rollup --config rollup.config.js --sourcemap", "build": "npm run clean && npm run rollup", "clean": "rm -r dist", "watch": "npm run build && SHELL=/bin/bash chokidar 'src/**/*.js' -c 'npm run format && npm run build'", "format": "prettier --write --single-quote --trailing-comma es5 'src/**/*.js'", "format:lint": "prettier -l --single-quote --trailing-comma es5 'src/**/*.js'", "release": "./release"}, "license": "MIT"}