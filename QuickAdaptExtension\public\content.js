import React from 'react';
import { createRoot } from 'react-dom/client';
import Drawer from '../src/components/drawer/Drawer';
import App from '../src/App'
import { initScraper } from '../src/services/scraper';

// Check if our app container already exists
const existingContainer = document.getElementById('my-react-drawer');
if (!existingContainer) {
  // Only create the container if it doesn't exist
  const appContainer = document.createElement('div');
  appContainer.id = 'my-react-drawer';
  document.body.appendChild(appContainer);

  // Check if we should render on this site
  const restrictedSites = ["user.quickadopt.in", "web.quickadopt.in", "google.com", "127.0.0.1"];
  const currentSite = window.location.hostname;

  if (!restrictedSites.some(site => currentSite.includes(site))) {
    // Check if this tab should have the extension open
    chrome.runtime.sendMessage({ action: "checkTabStatus" }, (response) => {
      if (response && response.shouldOpen) {
        // Now get the state and render
        chrome.runtime.sendMessage({ action: "getState" }, (stateResponse) => {
          const savedState = stateResponse ? stateResponse.state : null;

          const root = createRoot(document.getElementById('my-react-drawer'));
          root.render(<App initialState={savedState} />);
        });

        // Set up event listener for beforeunload to save state
        window.addEventListener('beforeunload', () => {
          if (window.appState) {
            chrome.runtime.sendMessage({
              action: "saveState",
              state: window.appState
            });
          }
        });
        initScraper();

      }
      else {
        // Remove the container if extension shouldn't be open in this tab
        appContainer.remove();
      }
    });

    let observer = null;
    let isScraping = false;

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'startClickScraping') {
        isScraping = true;
        console.log('Click-based scraping started in content script');
        sendResponse({ success: true });
        return true;
      }

      if (message.action === 'stopClickScraping') {
        isScraping = false;
        console.log('Click-based scraping stopped in content script');
        sendResponse({ success: true });
        return true;
      }

      // Legacy support for old scraping methods
      if (message.action === 'startScraping') {
        isScraping = true;
         if (observer) observer.disconnect();
        import('./scraper').then(({ scrapeDOM }) => {
          scrapeDOM(message.maxDepth).then(data => {
            chrome.runtime.sendMessage({ action: 'startScraping', data, append: true });
          });
        });

        // Start observing DOM changes
        if (message.observeMutations) {
          startDOMObserver(message.maxDepth);
        }

        sendResponse({ success: true });
        return true;
      }

      if (message.action === 'stopScraping') {
        if (observer) {
          observer.disconnect();
          observer = null;
        }
        isScraping = false;
        sendResponse({ success: true });
        return true;
      }
    });

    // Add event listeners for custom events as fallback
    window.addEventListener('quickadapt-start-click-scraping', () => {
      isScraping = true;
      console.log('Click-based scraping started via custom event');
    });

    window.addEventListener('quickadapt-stop-click-scraping', () => {
      isScraping = false;
      console.log('Click-based scraping stopped via custom event');
    });

    // Legacy event listeners for backward compatibility
    window.addEventListener('quickadapt-start-scraping', (event) => {
      const detail = event.detail || {};
      const maxDepth = detail.maxDepth || 3;

      isScraping = true;
      if (observer) observer.disconnect();

      import('./scraper').then(({ scrapeDOM }) => {
        scrapeDOM(maxDepth).then(data => {
          chrome.runtime.sendMessage({ action: 'startScraping', data, append: true });
        });
      });

      // Start observing DOM changes
      if (detail.observeMutations) {
        startDOMObserver(maxDepth);
      }
    });

    window.addEventListener('quickadapt-stop-scraping', () => {
      if (observer) {
        observer.disconnect();
        observer = null;
      }
      isScraping = false;
    });


    function startDOMObserver(maxDepth = 3){
      if (observer) observer.disconnect(); // Clear existing

      observer = new MutationObserver((mutations) => {
        let shouldScrape = false;

        for (const mutation of mutations) {
          if (
            mutation.type === 'childList' &&
            (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)
          ) {
            shouldScrape = true;
            break;
          }


        if (
          mutation.type === 'attributes' &&
          (mutation.attributeName === 'style' || mutation.attributeName === 'class')
        ) {
          shouldScrape = true;
          break;
        }
      }
        if (shouldScrape) {
          if (scrapeTimeout) clearTimeout(scrapeTimeout);

          // Debounce: wait 300ms after last mutation
          scrapeTimeout = setTimeout(() => {
            import('./scraper').then(({ scrapeDOM }) => {
              scrapeDOM(maxDepth).then(data => {
                chrome.runtime.sendMessage({ action: 'startScraping', data, append: true });
              });
            });
          }, 300);
        }
      });

      observer.observe(document.documentElement, {
        childList: true,
        subtree: true
      });
    }




    function removeExtensionContainer() {
      const container = document.getElementById('my-react-drawer');
      if (container) {
        container.remove();
      }
    }


    // Send message to background script to mark tab as closed
    function closeExtension() {
      chrome.runtime.sendMessage({ action: "closeExtension" }, (response) => {
        if (response && response.success) {
          removeExtensionContainer();
        }
      });
    }
       (function() {
  const htmlDir = document.documentElement.getAttribute('dir');
  const bodyDir = document.body.getAttribute('dir');
  if (htmlDir === 'rtl' || bodyDir === 'rtl') {
    document.body.classList.add('rtl');
  }
})();
    // You can attach this to a global window object if needed
    window.closeExtension = closeExtension;

  } else {
    // Remove the container on restricted sites
    appContainer.remove();
    console.log("Extension restricted on this site.");
  }
} else {
  console.log("QuickAdopt already initialized on this page.");
}
// === Inject invisible marker for extension detection ===
const quickadaptMarkerId = 'quickadapt-extension-marker';

// --- BEGIN: Handle quickadopt_guide_id param ---
(function handleQuickAdoptGuideId() {
  try {
    const url = new URL(window.location.href);
    const guideId = url.searchParams.get('quickadopt_guide_id');
    if (guideId) {
      sessionStorage.setItem('pending_guide_id', guideId);
      url.searchParams.delete('quickadopt_guide_id');
      window.history.replaceState({}, document.title, url.toString());
      // Optionally, send a custom event to notify React app
      window.dispatchEvent(new CustomEvent('quickadopt-guide-id-detected', { detail: { guideId } }));
    }
  } catch (e) { /* ignore */ }
})();
// --- END: Handle quickadopt_guide_id param ---

if (!document.getElementById(quickadaptMarkerId)) {
  const markerElement = document.createElement('div');
  markerElement.id = quickadaptMarkerId;
  markerElement.style.display = 'none';
  document.documentElement.appendChild(markerElement);
}

