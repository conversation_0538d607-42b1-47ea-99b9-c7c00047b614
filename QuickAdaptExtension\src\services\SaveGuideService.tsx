import { AxiosResponse } from "axios";
import { adminApiService } from "./APIService";

export interface SaveGuideRequest {
	GuideId: string;
	GuideType: string;
	Name: string;
	Content: string;
	OrganizationId: string;
	CreatedDate: string;
	UpdatedDate: string;
	CreatedBy: string;
	UpdatedBy: string;
	TargetUrl: string;
	Frequency: string;
	Segment: string;
	AccountId: string;
	GuideStatus: string;
	GuideStep: Array<any>;
}

export const saveGuide = async (
	guideData: SaveGuideRequest,
	onSuccess: (response?: AxiosResponse<any, any>) => void,
	onError: (error: any) => void,
	setLoading: (loading: boolean) => void
) => {
	try {
		setLoading(true);
		const response = await adminApiService.post("/Guide/Saveguide", guideData);

		if (response && response.status === 200 && response.data.Success) {
			onSuccess(response);
		} else {
			console.error("Unexpected response:", response);
			const err = "Unexpected response status";
			const errorMessage = response?.data?.ErrorMessage || err;
			onError(errorMessage);
		}
	} catch (error) {
		console.error("Error saving guide:", error);
		onError(error);
	} finally {
		setLoading(false);
	}
};

export const updateGuide = async (
	guideData: SaveGuideRequest,
	onSuccess: (response?: AxiosResponse<any, any>) => void,
	onError: (error: any) => void,
	setLoading: (loading: boolean) => void
) => {
	try {
		setLoading(true);
		const response = await adminApiService.post("/Guide/Updateguide", guideData);

		if (response && response.status === 200 && response.data.Success) {
			onSuccess(response);
		} else {
			console.error("Unexpected response:", response);
			const err = "Unexpected response status";
			const errorMessage = response?.data?.ErrorMessage || err;
			onError(errorMessage);
		}
	} catch (error) {
		console.error("Error saving guide:", error);
		onError(error);
	} finally {
		setLoading(false);
	}
};
