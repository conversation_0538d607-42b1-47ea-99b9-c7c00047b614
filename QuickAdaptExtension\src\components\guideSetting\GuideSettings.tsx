import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Typography, IconButton, Tooltip } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DesignServicesIcon from "@mui/icons-material/DesignServices";
import ViewModuleIcon from "@mui/icons-material/ViewModule";
import CodeIcon from "@mui/icons-material/Code";
import PageTrigger from "./PageTrigger";
import ElementRules from "./ElementRules";

//import "./GuideSettings.css";
// import Draggable from "react-draggable";

const GuideSetting = () => {
	// State to control the visibility of CanvasSettings
	const [showPageTrigger, setShowPageTrigger] = useState(false);
	const [showUrlRules, setShowUrlRules] = useState(false);
	const [showElementUrls, setShowElementUrls] = useState(false);
	const [isOpen, setIsOpen] = useState(true);

	const toggleCanvasSettings = () => {
		setShowPageTrigger(!showPageTrigger);
	};

	const toggleElementsSettings = () => {
		setShowUrlRules(!showUrlRules);
	};

	const toggleCustomCSS = () => {
		setShowElementUrls(!showElementUrls); // Toggle CustomCSS visibility
	};
	const handleClose = () => {
		setIsOpen(false); // Close the popup when close button is clicked
	};

	if (!isOpen) return null;

	return (
		//<Draggable>
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				{/* Header with title and close button */}
				<div className="qadpt-design-header">
					<div className="qadpt-title">Settings</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>
				<div>
					<Typography
						sx={{
							color: "var(--primarycolor)",
							position: "relative",
							fontSize: "14px",
						}}
					>
						settings for current page{" "}
					</Typography>
				</div>
				<div className="qadpt-controls">				{/* Buttons with icons */}
				<Button
					fullWidth
					sx={{
						justifyContent: "flex-start",
						backgroundColor: "#ede8e7",
						color: "#495e58",
						textTransform: "none",
						marginBottom: "8px",
						borderRadius: "12px",
						padding: "8px",
						":hover": {
							backgroundColor: "#d8d4d2",
						},
					}}
					onClick={toggleCanvasSettings} // Show/Hide CanvasSettings
					startIcon={<DesignServicesIcon />}
				>
					On Page Trigger
				</Button>
				<Tooltip
					title="Coming Soon"
					PopperProps={{
						sx: {
							zIndex: 9999,
						},
					}}
				>
					<span>
						{" "}
						{/* Wrapper for the disabled Button */}
						<Button
							fullWidth
							disabled
							sx={{
								justifyContent: "flex-start",
								backgroundColor: "#ede8e7",
								color: "#495e58",
								textTransform: "none",
								marginBottom: "8px",
								borderRadius: "12px",
								padding: "8px",
								":hover": {
									backgroundColor: "#d8d4d2",
								},
							}}
							onClick={toggleElementsSettings}
							startIcon={<ViewModuleIcon />}
						>
							Url Rules
						</Button>
					</span>
				</Tooltip>
				<Tooltip
					title="Coming Soon"
					PopperProps={{
						sx: {
							zIndex: 9999,
						},
					}}
				>
					<span>
						{" "}
						{/* Wrapper for the disabled Button */}
						<Button
							fullWidth
							disabled
							sx={{
								justifyContent: "flex-start",
								backgroundColor: "#ede8e7",
								color: "#495e58",
								textTransform: "none",
								borderRadius: "12px",
								padding: "8px",
								":hover": {
									backgroundColor: "#d8d4d2",
								},
							}}
							onClick={toggleCustomCSS}
							startIcon={<CodeIcon />}
						>
							Element Rules
						</Button>
					</span>
					</Tooltip>
					</div>

			</div>
			{/* Conditionally render CanvasSettings */}
			{showPageTrigger && (
				<Box
					sx={{
						marginTop: "16px", // Add some margin to separate from buttons
						border: "1px solid #ddd", // Optional styling for CanvasSettings container
						padding: "8px",
						position: "relative",
						bottom: "200px",
						zIndex: 9999,
					}}
				>
					<PageTrigger />
				</Box>
			)}
			{showUrlRules && (
				<Box
					sx={{
						marginTop: "16px", // Add some margin to separate from buttons
						border: "1px solid #ddd", // Optional styling for CanvasSettings container
						padding: "8px",
						position: "relative",
						bottom: "100px",
						zIndex: 9999,
					}}
				>
					{/* <Elementssettings /> */}
				</Box>
			)}
			{showElementUrls && (
				<Box
					sx={{
						marginTop: "16px", // Add some margin to separate from buttons
						border: "1px solid #ddd", // Optional styling for CustomCSS container
						padding: "8px",
						position: "relative",
						bottom: "100px",
						zIndex: 9999,
					}}
				>
					<ElementRules />
				</Box>
			)}
		</div>
		//	</Draggable>
	);
};

export default GuideSetting;
