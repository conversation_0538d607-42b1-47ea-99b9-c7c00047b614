{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\common\\\\UndoRedoButtons.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { IconButton, Tooltip, Box } from '@mui/material';\nimport { useTranslation } from 'react-i18next';\nimport useDrawerStore from '../../store/drawerStore';\nimport { redoicon, undoicon } from '../../assets/icons/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n/**\r\n * UndoRedoButtons component provides undo and redo functionality\r\n * for the guide editor.\r\n */\nconst UndoRedoButtons = ({\n  size = 'medium',\n  color = 'primary',\n  disabled = false,\n  className\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  // Get the undo/redo functions and state from the drawer store\n  const {\n    undo,\n    redo,\n    canUndo,\n    canRedo\n  } = useDrawerStore();\n\n  // Check if undo/redo are available\n  const canUndoValue = canUndo();\n  const canRedoValue = canRedo();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"undo-redobtn\",\n    sx: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: translate(\"coming soon\"),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            className: \"qadpt-banner-button qadpt-icon\",\n            onClick: undo,\n            disabled: true,\n            sx: {\n              opacity: 0.5\n            },\n            size: size,\n            color: \"default\",\n            \"aria-label\": translate(\"Undo\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: undoicon\n              },\n              style: {\n                width: \"24px\",\n                placeContent: \"center\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        marginLeft: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: translate(\"coming soon\"),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            className: \"qadpt-banner-button qadpt-icon\",\n            onClick: redo,\n            disabled: true,\n            sx: {\n              opacity: 0.5\n            },\n            size: size,\n            color: \"default\",\n            \"aria-label\": translate(\"Redo\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: redoicon\n              },\n              style: {\n                width: \"24px\",\n                placeContent: \"center\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(UndoRedoButtons, \"5hsfWG4uy1RzyqsCknNj2iYU838=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = UndoRedoButtons;\nexport default UndoRedoButtons;\nvar _c;\n$RefreshReg$(_c, \"UndoRedoButtons\");", "map": {"version": 3, "names": ["React", "IconButton", "<PERSON><PERSON><PERSON>", "Box", "useTranslation", "useDrawerStore", "redoicon", "undoicon", "jsxDEV", "_jsxDEV", "UndoRedoButtons", "size", "color", "disabled", "className", "_s", "t", "translate", "undo", "redo", "canUndo", "canRedo", "canUndoValue", "canRedoValue", "sx", "display", "alignItems", "children", "arrow", "title", "onClick", "opacity", "dangerouslySetInnerHTML", "__html", "style", "width", "place<PERSON><PERSON>nt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/UndoRedoButtons.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { IconButton, Tooltip, Box } from '@mui/material';\r\nimport UndoIcon from '@mui/icons-material/Undo';\r\nimport RedoIcon from '@mui/icons-material/Redo';\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore from '../../store/drawerStore';\r\nimport { redoicon, undoicon } from '../../assets/icons/icons';\r\n\r\ninterface UndoRedoButtonsProps {\r\n  size?: 'small' | 'medium' | 'large';\r\n  color?: string;\r\n  disabled?: boolean;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * UndoRedoButtons component provides undo and redo functionality\r\n * for the guide editor.\r\n */\r\nconst UndoRedoButtons: React.FC<UndoRedoButtonsProps> = ({\r\n  size = 'medium',\r\n  color = 'primary',\r\n  disabled = false,\r\n  className,\r\n}) => {\r\n  const { t: translate } = useTranslation();\r\n  // Get the undo/redo functions and state from the drawer store\r\n  const { undo, redo, canUndo, canRedo } = useDrawerStore();\r\n\r\n  // Check if undo/redo are available\r\n  const canUndoValue = canUndo();\r\n  const canRedoValue = canRedo();\r\n\r\n  return (\r\n    <Box className=\"undo-redobtn\" sx={{ display: 'flex', alignItems: 'center' }}>\r\n      <Box>\r\n        <Tooltip arrow title={translate(\"coming soon\")}>\r\n          <span>\r\n            {/* <IconButton\r\n              onClick={undo}\r\n              disabled={disabled || !canUndoValue}\r\n              size={size}\r\n              color=\"default\"\r\n              aria-label={translate(\"Undo\")}\r\n            >\r\n              <UndoIcon fontSize={size} />\r\n            </IconButton> */}\r\n            <IconButton\r\n              className=\"qadpt-banner-button qadpt-icon\"\r\n              onClick={undo}\r\n              disabled={true}\r\n              sx={{ opacity: 0.5 }}\r\n              size={size}\r\n              color=\"default\"\r\n              aria-label={translate(\"Undo\")}\r\n            >\r\n              <span dangerouslySetInnerHTML={{ __html: undoicon }} style={{ width: \"24px\", placeContent: \"center\" }}></span>\r\n            </IconButton>\r\n          </span>\r\n        </Tooltip>\r\n      </Box>\r\n      <Box sx={{ marginLeft: 1 }}>\r\n        <Tooltip arrow title={translate(\"coming soon\")}>\r\n          <span>\r\n            {/* <IconButton\r\n              onClick={redo}\r\n              disabled={disabled || !canRedoValue}\r\n              size={size}\r\n              color=\"default\"\r\n              aria-label={translate(\"Redo\")}\r\n            >\r\n              <RedoIcon fontSize={size} />\r\n            </IconButton> */}\r\n            <IconButton\r\n              className=\"qadpt-banner-button qadpt-icon\"\r\n              onClick={redo}\r\n              disabled={true}\r\n              sx={{ opacity: 0.5 }}\r\n              size={size}\r\n              color=\"default\"\r\n              aria-label={translate(\"Redo\")}\r\n            >\r\n              <span dangerouslySetInnerHTML={{ __html: redoicon }} style={{ width: \"24px\", placeContent: \"center\" }}></span>\r\n            </IconButton>\r\n          </span>\r\n        </Tooltip>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default UndoRedoButtons;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,OAAO,EAAEC,GAAG,QAAQ,eAAe;AAGxD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS9D;AACA;AACA;AACA;AACA,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,IAAI,GAAG,QAAQ;EACfC,KAAK,GAAG,SAAS;EACjBC,QAAQ,GAAG,KAAK;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGb,cAAc,CAAC,CAAC;EACzC;EACA,MAAM;IAAEc,IAAI;IAAEC,IAAI;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGhB,cAAc,CAAC,CAAC;;EAEzD;EACA,MAAMiB,YAAY,GAAGF,OAAO,CAAC,CAAC;EAC9B,MAAMG,YAAY,GAAGF,OAAO,CAAC,CAAC;EAE9B,oBACEZ,OAAA,CAACN,GAAG;IAACW,SAAS,EAAC,cAAc;IAACU,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAC1ElB,OAAA,CAACN,GAAG;MAAAwB,QAAA,eACFlB,OAAA,CAACP,OAAO;QAAC0B,KAAK;QAACC,KAAK,EAAEZ,SAAS,CAAC,aAAa,CAAE;QAAAU,QAAA,eAC7ClB,OAAA;UAAAkB,QAAA,eAUElB,OAAA,CAACR,UAAU;YACTa,SAAS,EAAC,gCAAgC;YAC1CgB,OAAO,EAAEZ,IAAK;YACdL,QAAQ,EAAE,IAAK;YACfW,EAAE,EAAE;cAAEO,OAAO,EAAE;YAAI,CAAE;YACrBpB,IAAI,EAAEA,IAAK;YACXC,KAAK,EAAC,SAAS;YACf,cAAYK,SAAS,CAAC,MAAM,CAAE;YAAAU,QAAA,eAE9BlB,OAAA;cAAMuB,uBAAuB,EAAE;gBAAEC,MAAM,EAAE1B;cAAS,CAAE;cAAC2B,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAS;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eACN/B,OAAA,CAACN,GAAG;MAACqB,EAAE,EAAE;QAAEiB,UAAU,EAAE;MAAE,CAAE;MAAAd,QAAA,eACzBlB,OAAA,CAACP,OAAO;QAAC0B,KAAK;QAACC,KAAK,EAAEZ,SAAS,CAAC,aAAa,CAAE;QAAAU,QAAA,eAC7ClB,OAAA;UAAAkB,QAAA,eAUElB,OAAA,CAACR,UAAU;YACTa,SAAS,EAAC,gCAAgC;YAC1CgB,OAAO,EAAEX,IAAK;YACdN,QAAQ,EAAE,IAAK;YACfW,EAAE,EAAE;cAAEO,OAAO,EAAE;YAAI,CAAE;YACrBpB,IAAI,EAAEA,IAAK;YACXC,KAAK,EAAC,SAAS;YACf,cAAYK,SAAS,CAAC,MAAM,CAAE;YAAAU,QAAA,eAE9BlB,OAAA;cAAMuB,uBAAuB,EAAE;gBAAEC,MAAM,EAAE3B;cAAS,CAAE;cAAC4B,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAS;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAtEIL,eAA+C;EAAA,QAM1BN,cAAc,EAEEC,cAAc;AAAA;AAAAqC,EAAA,GARnDhC,eAA+C;AAwErD,eAAeA,eAAe;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}