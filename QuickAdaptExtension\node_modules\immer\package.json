{"name": "immer", "version": "10.1.1", "description": "Create your next immutable state by mutating the current one", "main": "./dist/cjs/index.js", "module": "./dist/immer.legacy-esm.js", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/immer.d.ts", "import": "./dist/immer.mjs", "require": "./dist/cjs/index.js"}}, "jsnext:main": "dist/immer.mjs", "react-native": "./dist/immer.legacy-esm.js", "source": "src/immer.ts", "types": "./dist/immer.d.ts", "sideEffects": false, "scripts": {"pretest": "yarn build", "test": "jest && yarn test:build && yarn test:flow", "test:perf": "cd __performance_tests__ && node add-data.mjs && node todo.mjs && node incremental.mjs && node large-obj.mjs", "test:flow": "yarn flow check __tests__/flow", "test:build": "NODE_ENV='production' yarn jest --config jest.config.build.js", "watch": "jest --watch", "coverage": "jest --coverage", "coveralls": "jest --coverage && cat ./coverage/lcov.info | ./node_modules/.bin/coveralls && rm -rf ./coverage", "build": "tsup", "publish-docs": "cd website && GIT_USER=mweststrate USE_SSH=true yarn docusaurus deploy", "start": "cd website && yarn start", "test:size": "yarn build && yarn import-size --report . produce enableMapSet enablePatches", "test:sizequick": "yarn build && yarn import-size . produce"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "https://github.com/immerjs/immer.git"}, "keywords": ["immutable", "mutable", "copy-on-write"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}, "bugs": {"url": "https://github.com/immerjs/immer/issues"}, "homepage": "https://github.com/immerjs/immer#readme", "files": ["dist", "compat", "src"], "devDependencies": {"@babel/core": "^7.21.3", "@types/jest": "^25.1.2", "coveralls": "^3.0.0", "cpx2": "^3.0.0", "deep-freeze": "^0.0.1", "flow-bin": "^0.123.0", "husky": "^1.2.0", "immutable": "^3.8.2", "import-size": "^1.0.2", "jest": "^29.5.0", "lodash": "^4.17.4", "lodash.clonedeep": "^4.5.0", "prettier": "1.19.1", "pretty-quick": "^1.8.0", "redux": "^4.0.5", "rimraf": "^2.6.2", "seamless-immutable": "^7.1.3", "semantic-release": "^17.0.2", "ts-jest": "^29.0.0", "tsup": "^6.7.0", "typescript": "^5.0.2"}}