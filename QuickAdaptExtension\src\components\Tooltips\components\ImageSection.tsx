import React, { useEffect, useState } from "react";
import { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from "@mui/material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import DriveFolderUploadIcon from "@mui/icons-material/DriveFolderUpload";
import BackupIcon from "@mui/icons-material/Backup";
import Modal from "@mui/material/Modal";
import SelectImageFromApplication from "../../common/SelectImageFromApplication";
import { useTranslation } from "react-i18next";

import {
	uploadfile,
	hyperlink,
	files,
	uploadicon,
	replaceimageicon,
	copyicon,
	deleteicon,
	sectionheight,
	Settings,
	CrossIcon,
} from "../../../assets/icons/icons";
import useDrawerStore, {
	IMG_CONTAINER_DEFAULT_HEIGHT,
	IMG_CONTAINER_MAX_HEIGHT,
	IMG_CONTAINER_MIN_HEIGHT,
	IMG_EXPONENT,
	IMG_OBJECT_FIT,
	IMG_STEP_VALUE,
	TImageContainer,
} from "../../../store/drawerStore";
import { ChromePicker, ColorResult } from "react-color";
import "../../guideSetting/PopupSections/PopupSections.css";
import { getAllFiles } from "../../../services/FileService";
import { FileUpload } from "../../../models/FileUpload";

const ImageSection: React.FC<{ items: TImageContainer & { type: "image" }; isCloneDisabled?: boolean; }> = ({ items: imagesContainer, isCloneDisabled }) => {
	const { t: translate } = useTranslation();
	const {
		uploadTooltipImage: uploadImage,
		imageAnchorEl,
		setImageAnchorEl,
		replaceTooltipImage: replaceImage,
		cloneTooltipImage: cloneImageContainer,
		deleteTooltipImageContainer: deleteImageContainer,
		updateTooltipImageContainer: updateImageContainer, // TODO
		toggleTooltipImageFit: toggleFit, //TODO
		setImageSrc: storeImageSrc,
		tooltip,
	} = useDrawerStore((state: any) => state);

	const [snackbarOpen, setSnackbarOpen] = useState(false);
		const [snackbarMessage, setSnackbarMessage] = useState('');
		const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');
	
		const [snackbarKey, setSnackbarKey] = useState<number>(0); 
	
		const openSnackbar = () => {
			setSnackbarKey(prev => prev + 1);
			setSnackbarOpen(true);
		};
		const closeSnackbar = () => {
			setSnackbarOpen(false);
		};

	const [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({
		currentContainerId: "",
		isOpen: false,
	});
	const [imageLink, setImageLink] = useState<string>("");
	const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);
	const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{
		currentContainerId: string;
		isImage: boolean;
		height: number;
	}>({ currentContainerId: "", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });

	const [selectedAction, setSelectedAction] = useState("none");
	const [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);
	const [selectedColor, setSelectedColor] = useState<string>("");
	const [isModelOpen, setModelOpen] = useState(false);
	const [formOfUpload, setFormOfUpload] = useState<String>("");
	const [isReplaceImage, setReplaceImage] = useState(false);

	const openSettingsPopover = Boolean(settingsAnchorEl);
	const handleActionChange = (event: any) => {
		setSelectedAction(event.target.value);
	};
	const handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {
		setSettingsAnchorEl(event.currentTarget);
	};

	const handleCloseSettingsPopover = () => {
		setSettingsAnchorEl(null);
	};

	const imageContainerStyle: React.CSSProperties = {
		width: "100%",
		height: `${imagesContainer.style.height}px`,
		display: "flex",
		justifyContent: "center",
		alignItems: "center",
		padding: 0,
		margin: 0,
		overflow: "hidden",
	};

	const imageStyle: React.CSSProperties = {
		width: "100%",
		height: "100%",
		margin: 0,
		padding: 0,
		borderRadius: "0",
	};

	const iconRowStyle: React.CSSProperties = {
		display: "flex",
		justifyContent: "center",
		gap: "16px",
		marginTop: "10px",
	};

	const iconTextStyle: React.CSSProperties = {
		display: "flex",
		flexDirection: "column",
		alignItems: "center",
		justifyContent: "center",
		width: "100%",
	};

	const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		let urll: any;
		if (file) {
			const parts = file.name.split('.');
   			const extension = parts.pop();
			// Check for double extensions (e.g. file.html.png) or missing/invalid extension
   			 if (parts.length > 1 || !extension ) {
			  setSnackbarMessage("Uploaded file name should not contain any special character");
       		 setSnackbarSeverity("error");
			 setSnackbarOpen(true);
			 event.target.value = '';
      		 return;
			 
   			 }
			 if(file.name.length > 128){
				setSnackbarMessage("File name should not exceed 128 characters");
       			setSnackbarSeverity("error");
			 	setSnackbarOpen(true);
			 	event.target.value = '';
      		 	return;
			 }
			// setImageName(event.target.files?.[0].name);

			const reader = new FileReader();
			reader.onloadend = () => {
				const base64Image = reader.result as string;
				storeImageSrc(base64Image);
				// urll = base64Image;
				uploadImage(imagesContainer.id, {
					altText: file.name,
					id: crypto.randomUUID(),
					url: base64Image,
					backgroundColor: "#ffffff",
					objectFit: IMG_OBJECT_FIT,
				});
			};
			reader.readAsDataURL(file);
		}
	};

	const handleImageUploadFormApp = (file: FileUpload) => {
		if (file) {
			storeImageSrc(file.Url);
			if (isReplaceImage) {
				replaceImage(imagesContainer.id, {
					altText: file.FileName,
					url: file.Url,
					backgroundColor: "#ffffff",
					objectFit: IMG_OBJECT_FIT,
				});
				setReplaceImage(false);
			} else {
				uploadImage(imagesContainer.id, {
					altText: file.FileName,
					id: crypto.randomUUID(), // Use existing ID
					url: file.Url, // Directly use the URL
					backgroundColor: "#ffffff",
					objectFit: IMG_OBJECT_FIT,
				});
			}
		}
		setModelOpen(false);
	};

	const handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onloadend = () => {
				replaceImage(imagesContainer.id, {
					altText: file.name,
					url: reader.result,
					backgroundColor: "#ffffff",
					objectFit: IMG_OBJECT_FIT,
				});
			};
			reader.readAsDataURL(file);
		}
	};

	const handleClick = (
		event: React.MouseEvent<HTMLElement>,
		containerId: string,
		imageId: string,
		isImage: boolean,
		currentHeight: number
	) => {
		// @ts-ignore
		if (["file-upload", "hyperlink"].includes(event.target.id)) return;
		setImageAnchorEl({
			buttonId: imageId,
			containerId: containerId,
			// @ts-ignore
			value: event.currentTarget,
		});
		setSettingsAnchorEl(null);
		setCurrentImageSectionInfo({
			currentContainerId: containerId,
			isImage,
			height: currentHeight,
		});
		setShowHyperlinkInput({
			currentContainerId: "",
			isOpen: false,
		});
	};

	const handleClose = () => {
		setImageAnchorEl({
			buttonId: "",
			containerId: "",
			// @ts-ignore
			value: null,
		});
		setSettingsAnchorEl(null);
	};

	const open = Boolean(imageAnchorEl.value && imagesContainer.id === imageAnchorEl.containerId);
	const colorPickerOpen = Boolean(colorPickerAnchorEl);

	const id = open ? "image-popover" : undefined;

	const handleIncreaseHeight = (prevHeight: number) => {
		if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;
		const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);
		updateImageContainer(imagesContainer.id, "style", {
			height: newHeight,
		});
		setCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));
	};

	const handleDecreaseHeight = (prevHeight: number) => {
		if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;
		const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);
		updateImageContainer(imagesContainer.id, "style", {
			height: newHeight,
		});
		setCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));
	};

	const triggerImageUpload = () => {
		// setReplaceImage(true);
		// setModelOpen(true);
		document.getElementById("replace-upload")?.click();
	};

	// Function to delete the section
	const handleDeleteSection = () => {
		setImageAnchorEl({
			buttonId: "",
			containerId: "",
			// @ts-ignore
			value: null,
		});
		setSettingsAnchorEl(null);

		deleteImageContainer(imagesContainer.id);
	};
	const handleHyperlinkClick = (cId: string, isOpen: boolean) => {
		setShowHyperlinkInput({
			currentContainerId: cId,
			isOpen,
		});
	};

	const handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {
		if (event.key === "Enter" && imageLink) {
			uploadImage(imageAnchorEl.containerId, {
				altText: "New Image",
				id: crypto.randomUUID(),
				url: imageLink,
				backgroundColor: "transparent",
				objectFit: IMG_OBJECT_FIT,
			});
			setShowHyperlinkInput({
				currentContainerId: "",
				isOpen: false,
			});
		}
	};

	const handleCloneImgContainer = () => {
		cloneImageContainer(imagesContainer.id);
	};

	const handleCloseColorPicker = () => {
		setColorPickerAnchorEl(null);
	};

	const handleColorChange = (color: ColorResult) => {
		setSelectedColor(color.hex);
		updateImageContainer(imagesContainer.id, "style", {
			backgroundColor: color.hex,
		});
	};

	const handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {
		setColorPickerAnchorEl(event.currentTarget);
	};

	// console.log({ imageAnchorEl, tooltip });

	useEffect(() => {
		if (!tooltip.visible) {
			setImageAnchorEl({
				buttonId: "",
				containerId: "",
				// @ts-ignore
				value: null,
			});
			setSettingsAnchorEl(null);
		}
	}, [tooltip.visible]);

	return (
		<>
			<Box
				// key={id}
				sx={{
					width: "100%",
					height: "100%",
					display: "flex",
					flexDirection: "column",
					justifyContent: "flex-start",
					alignItems: "center",
					// padding: "5px",
					margin: "0px",
					overflow: "auto",
				}}
			>
				{!imagesContainer.images.length ? (
					<Box
						sx={{
							...imageContainerStyle,
							backgroundColor: imagesContainer.style.backgroundColor,
							height: `${imagesContainer.style.height}px`,
							textAlign: "center",
							width: "100%",
							// height: "100%",
							display: "flex",
							flexDirection: "column",
							justifyContent: "center",
						}}
						onClick={(e) => {
							// prevent to open toolbar when upload file clicked
							// @ts-ignore
							if (!e.target?.id.startsWith("file-upload")) {
								handleClick(
									e,
									imagesContainer.id,
									"",
									false,
									(imagesContainer?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT
								);
							}
						}}
						id={imagesContainer.id}
					>
						<Box
							sx={iconTextStyle}
							component={"div"}
						>
							<span
								dangerouslySetInnerHTML={{ __html: uploadfile }}
								style={{ display: "inline-block" }}
							/>
							<Typography
								variant="h6"
								align="center"
								color="textSecondary"
								sx={{ fontSize: "14px", fontWeight: "600" }}
							>
								{translate("Upload file", { defaultValue: "Upload file" })}
							</Typography>
						</Box>

						<Typography
							variant="body2"
							align="center"
							color="textSecondary"
							sx={{ fontSize: "14px" }}
						>
							{translate("Drag & Drop to upload file", { defaultValue: "Drag & Drop to upload file" })}
						</Typography>
						<Typography
							variant="body2"
							align="center"
							color="textSecondary"
							sx={{ marginTop: "8px", fontSize: "14px" }}
						>
							{translate("Or", { defaultValue: "Or" })}
						</Typography>
						{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (
							<TextField
								value={imageLink}
								onChange={(e) => setImageLink(e.target.value)}
								onKeyDown={handleLinkSubmit}
								autoFocus
							/>
						) : (
							<Box sx={iconRowStyle}>
									<Tooltip title={translate("Coming soon", { defaultValue: "Coming soon" })} arrow>
									<div style={{ pointerEvents: "auto", cursor: "pointer" }}>
										<span
											dangerouslySetInnerHTML={{ __html: hyperlink }}
											style={{
												color: "black",
												cursor: "pointer",
												fontSize: "32px",
												opacity: "0.5",
												pointerEvents: "none",
											}}
											id="hyperlink"
										/>
									</div>
								</Tooltip>

									<Tooltip title={translate("Coming soon", { defaultValue: "Coming soon" })} arrow>
									<span
										onClick={() => {
											//setModelOpen(true);
										}}
										dangerouslySetInnerHTML={{ __html: files }}
										style={{ color: "black", cursor: "pointer", fontSize: "32px", opacity: "0.5" }}
										id="folder"
										//title="Coming Soon"
									/>
								</Tooltip>
									<Tooltip title={translate("Upload File", { defaultValue: "Upload File" })} arrow>
									<span
										onClick={(event) => {
											event?.stopPropagation();
											document.getElementById(`file-upload-${imagesContainer.id}`)?.click();
										}}
										id="file-upload1"
										dangerouslySetInnerHTML={{ __html: uploadicon }}
										style={{ color: "black", cursor: "pointer", fontSize: "32px" }}
									/>
								</Tooltip>
								<input
									type="file"
									id={`file-upload-${imagesContainer.id}`}
									style={{ display: "none" }}
									accept="image/*"
									onChange={handleImageUpload}
								/>
								<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>
										<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
											{snackbarMessage}
										</Alert>
								</Snackbar>
							</Box>
						)}
					</Box>
				) : (
					imagesContainer.images.map((item: any) => {
						const imageSrc = item?.url;
						const imageId = item?.id;
						const objectFit = item?.objectFit || IMG_OBJECT_FIT;
						const currentSecHeight = (imagesContainer?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;
						const id = imagesContainer.id;
						return (
							<Box
								sx={{
									...imageContainerStyle,
									backgroundColor: imagesContainer.style.backgroundColor,
									height: `${imagesContainer.style.height}px`,
								}}
								onClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}
								component={"div"}
								id={id}
								onMouseOver={() => {
									setImageAnchorEl({
										buttonId: imageId,
										containerId: id,
										value: null,
									});
									setSettingsAnchorEl(null);
								}}
							>
								<img
									src={imageSrc}
									alt="Uploaded"
									style={{ ...imageStyle, objectFit }}
								/>
							</Box>
						);
					})
				)}
			</Box>

			<Popover
				// className="qadpt-imgsec-popover"
				id={"image-popover"}
				open={open}
				// anchorEl={document.getElementById("Tooltip-unique")}
				anchorReference="anchorPosition"
				anchorPosition={{
					left: document.getElementById("Tooltip-unique")?.getBoundingClientRect()?.x || 150,
					top: document.getElementById("Tooltip-unique")?.getBoundingClientRect()?.y || 80,
				}}
				onClose={handleClose}
				anchorOrigin={{
					vertical: "top",
					horizontal: "right",
				}}
				transformOrigin={{
					vertical: "bottom",
					horizontal: "left",
				}}
				
			>
				<Box
					// className="qadpt-tool-btn"
					sx={{
						display: "flex",
						// justifyContent: "space-between",
						alignItems: "center",
						gap: "15px",
						height: "100%",
						padding: "0 10px",
						fontSize: "12px",
					}}
				>
					<Box sx={{ display: "flex" }}>
						{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&
						currentImageSectionInfo.isImage ? (
							<>
								<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />
								<Typography
									fontSize="12px"
									marginLeft={"5px"}
									onClick={triggerImageUpload}
								>
										{translate("Replace Image", { defaultValue: "Replace Image" })}
								</Typography>
								<input
									type="file"
									id="replace-upload"
									style={{ display: "none" }}
									accept="image/*"
									onChange={handleReplaceImage}
								/>
							</>
						) : null}
					</Box>
					<Box
							className="qadpt-tool-items"
							sx={{ display: "flex", alignItems: "center" }}
						>
							<span dangerouslySetInnerHTML={{ __html: sectionheight }} />
						<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate("Minimum height reached", { defaultValue: "Minimum height reached" }) : translate("Decrease height", { defaultValue: "Decrease height" })}>
								<span>
									<IconButton
										onClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}
										size="small"
										disabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}
										sx={{
											opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,
											cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'
										}}
									>
										<RemoveIcon fontSize="small" />
									</IconButton>
								</span>
							</Tooltip>
							<Typography fontSize="12px">{currentImageSectionInfo.height}</Typography>
						<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate("Maximum height reached", { defaultValue: "Maximum height reached" }) : translate("Increase height", { defaultValue: "Increase height" })}>
								<span>
									<IconButton
										onClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}
										size="small"
										disabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}
										sx={{
											opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,
											cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'
										}}
									>
										<AddIcon fontSize="small" />
									</IconButton>
								</span>
							</Tooltip>
						</Box>
					<Tooltip title={translate("Settings", { defaultValue: "Settings" })} arrow>
						<Box className="qadpt-tool-items">
							<Box className="qadpt-tool-items">
								<IconButton
									size="small"
									onClick={handleSettingsClick}
								>
									<span
										dangerouslySetInnerHTML={{ __html: Settings }}
										style={{ color: "black" }}
									/>
								</IconButton>
							</Box>

							<Popover
								open={openSettingsPopover}
								anchorEl={settingsAnchorEl}
								id="image-properties"
								onClose={handleCloseSettingsPopover}
								anchorOrigin={{
									vertical: "center",
									horizontal: "right",
								}}
								disablePortal
								transformOrigin={{
									vertical: "center",
									horizontal: "left",
								}}
								slotProps={{
									root: {
										sx: {
											zIndex: (theme) => theme.zIndex.tooltip + 2000,
										},
									},
								}}
								PaperProps={{
									sx: {
										mt: 12,
										ml: 20,
										width: "205px",
									},
								}}
							>
								<Box p={2}>
									<Box
										display="flex"
										justifyContent="space-between"
										alignItems="center"
									>
										<Typography
											variant="subtitle1"
											sx={{ color: "rgba(95, 158, 160, 1)" }}
										>
											{translate("Image Properties", { defaultValue: "Image Properties" })}
										</Typography>
										<IconButton
											size="small"
											onClick={handleCloseSettingsPopover}
										>
											<span
												dangerouslySetInnerHTML={{ __html: CrossIcon }}
												style={{ color: "black" }}
											/>
										</IconButton>
									</Box>
									<Tooltip title={translate("Coming soon", { defaultValue: "Coming soon" })} arrow>
										<Box mt={2}>
											<Typography
												variant="body2"
												color="textSecondary"
												sx={{ marginBottom: "10px" }}
											>
												{translate("Image Actions", { defaultValue: "Image Actions" })}
											</Typography>
											<TextField
												select
												fullWidth
												variant="outlined"
												size="small"
												value={selectedAction}
												onChange={handleActionChange}
												sx={{
													"& .MuiOutlinedInput-root": {
														borderColor: "rgba(246, 238, 238, 1)",
													},
												}}
												disabled
											>
												<MenuItem value="none">{translate("None", { defaultValue: "None" })}</MenuItem>
												<MenuItem value="specificStep">{translate("Specific Step", { defaultValue: "Specific Step" })}</MenuItem>
												<MenuItem value="openUrl">{translate("Open URL", { defaultValue: "Open URL" })}</MenuItem>
												<MenuItem value="clickElement">{translate("Click Element", { defaultValue: "Click Element" })}</MenuItem>
												<MenuItem value="startTour">{translate("Start Tour", { defaultValue: "Start Tour" })}</MenuItem>
												<MenuItem value="startMicroSurvey">{translate("Start Micro Survey", { defaultValue: "Start Micro Survey" })}</MenuItem>
											</TextField>
										</Box>
									</Tooltip>
									<Box
										mt={2}
										component={"div"}
										id="toggle-fit"
									>
										<Typography
											variant="body2"
											color="textSecondary"
										>
											{translate("Image Formatting", { defaultValue: "Image Formatting" })}
										</Typography>
										<Box
											display="flex"
											gap={1}
											mt={1}
										>
											{["Fill", "Fit"].map((item) => {
												// Get current image's objectFit to determine selected state
												// imagesContainer is a single object, not an array
												const currentImage = imagesContainer.images.find((img) => img.id === imageAnchorEl.buttonId);
												const currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;

												// Determine if this button should be selected
												const isSelected = (item === "Fill" && currentObjectFit === "cover") ||
																  (item === "Fit" && currentObjectFit === "contain");
												return (
													<Button
														key={item}
														onClick={() => toggleFit(imagesContainer.id, item as "Fit" | "Fill")}
														variant="outlined"
														size="small"
														sx={{
															width: "88.5px",
															height: "41px",
															padding: "10px 12px",
															gap: "12px",
															borderRadius: "6px 6px 6px 6px",
															border:
																isSelected
																	? "1px solid rgba(95, 158, 160, 1)"
																	: "1px solid rgba(246, 238, 238, 1)",
															backgroundColor:
																isSelected ? "rgba(95, 158, 160, 0.2)" : "rgba(246, 238, 238, 0.5)",
															backgroundBlendMode: "multiply",
															color: "black",
															"&:hover": {
																backgroundColor:
																	isSelected ? "rgba(95, 158, 160, 0.3)" : "rgba(246, 238, 238, 0.6)",
															},
														}}
													>
														{translate(item, { defaultValue: item })}
													</Button>
												);
											})}
										</Box>
									</Box>
								</Box>
							</Popover>
						</Box>
					</Tooltip>
					<Tooltip title={translate("Background Color", { defaultValue: "Background Color" })} arrow>
						<Box className="qadpt-tool-items">
							<IconButton
								onClick={handleBackgroundColorClick}
								size="small"
								sx={{
									height: "20px",
									width: "20px",
									backgroundColor: imagesContainer.style.backgroundColor,
									border: "2px solid black",
									marginTop: "-3px",
								}}
							>
								{/* <span
									style={{
										backgroundColor: imagesContainer.style.backgroundColor,
										borderRadius: "100%",
										width: "20px",
										height: "20px",
										display: "inline-block",
										marginTop: "-3px",
									}}
								/> */}
							</IconButton>
						</Box>
					</Tooltip>
					<Tooltip title={isCloneDisabled ? translate("Maximum limit of 3 Image sections reached", { defaultValue: "Maximum limit of 3 Image sections reached" }) : translate("Clone Section", { defaultValue: "Clone Section" })}>
						<Box className="qadpt-tool-items">
							<IconButton
								onClick={handleCloneImgContainer}
								size="small"
								disabled={isCloneDisabled}
							>
								<span
									dangerouslySetInnerHTML={{ __html: copyicon }}
									style={{ opacity: isCloneDisabled ? 0.5 : 1 }}
								/>
							</IconButton>
							{/* cloneImageContainer */}
						</Box>
					</Tooltip>
					<Tooltip title={translate("Delete Section", { defaultValue: "Delete Section" })} arrow>
						<Box className="qadpt-tool-items">
							<IconButton
								onClick={handleDeleteSection}
								size="small"
								disabled={
									useDrawerStore((state) => state.toolTipGuideMetaData)[0]?.containers?.length === 1
								}
							>
								<span
									dangerouslySetInnerHTML={{ __html: deleteicon }}
									style={{
										opacity:
											useDrawerStore((state) => state.toolTipGuideMetaData)[0]?.containers?.length === 1
												? 0.5
												: 1,
										pointerEvents: 'none',
									}}
									// style={{ marginTop: "-3px" }}
								/>
							</IconButton>
						</Box>
					</Tooltip>
				</Box>
			</Popover>
			<Popover
				id="color-popover"
				open={colorPickerOpen}
				anchorEl={colorPickerAnchorEl}
				onClose={handleCloseColorPicker}
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "center",
				}}
				transformOrigin={{
					vertical: "top",
					horizontal: "center",
				}}
				slotProps={{
					root: {
						sx: {
							// zIndex: (theme) => theme.zIndex.tooltip + 1101,
							zIndex: "9999",
						},
					},
				}}
			>
				<Box>
					<ChromePicker
						color={imagesContainer.style.backgroundColor}
						onChange={handleColorChange}
					/>
					<style>
						{`
      .chrome-picker input {
        padding: 0 !important;
      }
    `}
					</style>
				</Box>
			</Popover>
			{isModelOpen && (
				<SelectImageFromApplication
					isOpen={isModelOpen}
					handleModelClose={() => setModelOpen(false)}
					onImageSelect={handleImageUploadFormApp}
				/>
			)}
		</>
	);
};

export default ImageSection;
