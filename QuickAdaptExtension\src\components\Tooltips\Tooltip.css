.highlight-border {
  outline: 2px solid red;
}

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */
  z-index: 9998; /* Below hotspot and popup */
  pointer-events: auto; /* Prevent interaction with the backdrop */
}

.highlighted-hotspot {
  position: absolute;
  z-index: 9999; /* Above the backdrop */
}

.popup {
  position: absolute;
  z-index: 10000; /* Above both the backdrop and hotspot */
}
