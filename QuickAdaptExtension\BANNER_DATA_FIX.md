# Banner Data Initialization Fix

## Problem Summary
When creating new banner steps in tours and navigating to them, the banner data was appearing empty. The canvas settings, positioning, and other banner-specific data were not being properly initialized or loaded when navigating to newly created banner steps.

## Root Causes Identified

### 1. **Missing Banner Data Initialization**
- New banner steps were not being properly added to the `bannerJson` structure
- Banner canvas settings were not being initialized with default values
- The `bannerJson.GuideStep` array was not being created if it didn't exist

### 2. **Data Loading Issues**
- When navigating to banner steps, the banner canvas settings were not being loaded
- The `loadBannerCanvasSettings` function was missing
- Banner data was not being synchronized between store and UI components

### 3. **Tooltip Metadata Issues**
- Banner steps in tours were missing proper tooltip metadata initialization
- Canvas settings in tooltip metadata were using wrong defaults for banner steps

## Fixes Applied

### 1. **Enhanced Banner Data Initialization** (`drawerStore.ts`)

#### Added `loadBannerCanvasSettings` Function
```typescript
loadBannerCanvasSettings: () => {
    set((state) => {
        if (state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Banner") {
            const currentBannerStep = state.bannerJson?.GuideStep?.[state.currentStep - 1];
            
            if (currentBannerStep?.Canvas) {
                // Load existing canvas settings
                const canvas = currentBannerStep.Canvas;
                state.Bposition = canvas.Position || CANVAS_DEFAULT_VALUE_Banner.position;
                state.bpadding = canvas.Padding || CANVAS_DEFAULT_VALUE_Banner.padding;
                // ... load all canvas properties
            } else {
                // Initialize with defaults and create the entry
                state.resetBannerCanvasToDefaults();
                // Create banner step entry in bannerJson
                // ... initialization logic
            }
        }
    });
}
```

#### Enhanced `createNewStep` Function
- **Proper bannerJson Initialization**: Ensures `bannerJson` structure exists before adding steps
- **Default Canvas Values**: Initializes banner steps with proper canvas defaults
- **Dual Data Storage**: Adds banner steps to both `bannerJson` and `updatedGuideData`

```typescript
// Ensure bannerJson structure exists
if (!state.bannerJson) {
    state.bannerJson = { GuideStep: [] };
}
if (!state.bannerJson.GuideStep) {
    state.bannerJson.GuideStep = [];
}

// Add the banner step to bannerJson
state.bannerJson.GuideStep = [...state.bannerJson.GuideStep, bannerGuideStep];

// Also add to the main updatedGuideData structure for tours
if (state.selectedTemplate === "Tour") {
    if (!state.updatedGuideData) {
        state.updatedGuideData = { GuideStep: [] };
    }
    state.updatedGuideData.GuideStep = [...state.updatedGuideData.GuideStep, bannerGuideStep];
}
```

#### Enhanced Tooltip Metadata Creation
- **Banner-Specific Canvas**: Uses `CANVAS_DEFAULT_VALUE_Banner` for banner steps
- **Proper Initialization**: Ensures tooltip metadata is created with correct defaults

```typescript
// Use banner canvas defaults for banner steps, regular canvas defaults for others
canvas: type === "Banner" ? CANVAS_DEFAULT_VALUE_Banner : CANVAS_DEFAULT_VALUE,
```

### 2. **Enhanced Navigation Logic** (`Drawer.tsx`)

#### Banner Data Loading on Navigation
```typescript
// CRITICAL FIX: Load banner canvas settings when navigating to banner steps
if (selectedTemplate === "Tour" && selectedStepType === "Banner") {
    console.log("🔄 handleStepChange: Loading banner canvas settings for step", targetStep.stepCount);
    setTimeout(() => {
        useDrawerStore.getState().loadBannerCanvasSettings();
    }, 0);
}
```

#### Missing Metadata Initialization
```typescript
// CRITICAL FIX: Ensure tooltip metadata exists for the step we're navigating to
if (selectedTemplate === "Tour") {
    const currentStepIndex = targetStep.stepCount - 1;
    const currentMetadata = useDrawerStore.getState().toolTipGuideMetaData[currentStepIndex];
    
    if (!currentMetadata) {
        // Initialize missing tooltip metadata with proper defaults
        const newMetadata = {
            // ... proper initialization with banner-specific canvas if needed
        };
        // Add the missing metadata
        updatedMetadata[currentStepIndex] = newMetadata;
    }
}
```

### 3. **Enhanced Store State Management**

#### Updated `setCurrentStep` Function
- **Automatic Banner Loading**: Calls `loadBannerCanvasSettings` when navigating to banner steps
- **Proper State Synchronization**: Ensures banner state is loaded from stored data

```typescript
// Load banner canvas settings when navigating to a banner step in tours
if (state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Banner") {
    // Use the new loadBannerCanvasSettings function to properly load banner data
    state.loadBannerCanvasSettings();
}
```

## Expected Behavior After Fix

### **Banner Step Creation**
1. User creates new banner step in tour
2. Banner step is properly initialized with default canvas settings
3. Data is stored in both `bannerJson` and `updatedGuideData`
4. Tooltip metadata is created with banner-specific defaults

### **Banner Step Navigation**
1. User navigates to banner step (manually or automatically)
2. `loadBannerCanvasSettings` is called automatically
3. Banner canvas data is loaded from `bannerJson`
4. UI components display proper banner settings
5. Missing metadata is initialized if needed

### **Data Consistency**
1. Banner position defaults to "Cover Top"
2. Banner background defaults to "#f1f1f7"
3. Banner padding defaults to "10"
4. All banner-specific settings are properly loaded

## Testing Instructions

### **Test 1: New Banner Step Creation**
1. Create new Tour guide
2. Create Step 1 as "Announcement"
3. Create Step 2 as "Banner"
4. **Expected**: Step 2 should have proper banner canvas settings loaded
5. **Verify**: Banner position should be "Cover Top", background "#f1f1f7"

### **Test 2: Banner Step Navigation**
1. Create tour with multiple steps including banner
2. Navigate between steps
3. Navigate to banner step
4. **Expected**: Banner data should load properly each time
5. **Verify**: Canvas settings should persist and display correctly

### **Test 3: Mixed Step Types**
1. Create tour: Step 1 (Announcement) → Step 2 (Banner) → Step 3 (Tooltip)
2. Navigate through all steps
3. **Expected**: Each step type should have proper data initialization
4. **Verify**: No empty data, proper defaults for each type

### **Test 4: Banner Settings Persistence**
1. Create banner step
2. Modify banner settings (position, colors, etc.)
3. Navigate to another step and back
4. **Expected**: Banner settings should persist
5. **Verify**: Modified settings are retained

## Console Output
When working correctly, you should see:
```
✅ createNewStep: Added banner step to bannerJson and updatedGuideData {stepTitle, stepCount, canvas}
🔄 handleStepChange: Loading banner canvas settings for step [stepCount]
🔄 loadBannerCanvasSettings: Loading banner data for step [stepCount] [bannerStepData]
✅ loadBannerCanvasSettings: Loaded existing canvas settings [canvas]
✅ handleStepChange: Initialized missing tooltip metadata [metadata]
```

## Validation Checklist
- [ ] Banner steps are created with proper canvas defaults
- [ ] Banner data is stored in both `bannerJson` and `updatedGuideData`
- [ ] Navigation to banner steps loads canvas settings automatically
- [ ] Banner position defaults to "Cover Top"
- [ ] Banner background defaults to "#f1f1f7"
- [ ] Tooltip metadata is properly initialized for banner steps
- [ ] Missing metadata is automatically created when needed
- [ ] Banner settings persist when navigating between steps
- [ ] No console errors during banner step creation/navigation
- [ ] UI displays proper banner canvas settings

This comprehensive fix ensures that banner steps in tours have proper data initialization and loading, eliminating the empty data issue and providing a seamless user experience.
