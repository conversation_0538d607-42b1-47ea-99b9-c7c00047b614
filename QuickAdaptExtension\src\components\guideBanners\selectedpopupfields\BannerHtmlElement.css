/* Button that triggers the popup */
.open-popup-btn {
    padding: 10px 20px;
    background-color: #5F9EA0;
    color: white;
    border: none;
    border-radius: var(--button-border-radius);
    cursor: pointer;
    font-size: 14px;
  }
  
  .open-popup-btn:hover {
    background-color:#5F9EA0;
  }
  
  /* Popup overlay to cover the screen */
  .popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Dark background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  /* Container for the popup content */
  .html-code-container {
    width: 300px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background-color: #f9f9f9;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .header h3 {
    font-size: 16px;
    font-weight: 500;
    color: #5F9EA0;
    margin: 0;
  }
  
  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #aaa;
    cursor: pointer;
  }
  
  .textarea-section {
    margin-bottom: 15px;
  }
  
  .textarea-section textarea {
    width: 100%;
    height: 100px;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    font-size: 14px;
    box-sizing: border-box;
  }
  
  .save-section {
    text-align: center;
  }
  
  .save-btn {
    padding: 10px 20px;
    background-color: #5F9EA0;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
  }
  
  .save-btn:hover {
    background-color: #5F9EA0;
  }
  