{"name": "@mui/utils", "version": "6.1.0", "private": false, "author": "MUI Team", "description": "Utility functions for React components.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "utils"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-utils"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "private package", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.25.6", "@types/prop-types": "^15.7.12", "clsx": "^2.1.1", "prop-types": "^15.8.1", "react-is": "^18.3.1", "@mui/types": "^7.2.16"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=14.0.0"}, "module": "./esm/index.js", "types": "./index.d.ts"}