{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\tours\\\\stepPopup.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport useDrawerStore from '../../store/drawerStore';\nimport { Select, MenuItem, Typography } from \"@mui/material\";\nimport { warning } from \"../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StepCreationPopup = props => {\n  _s();\n  var _steps;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    isOpen,\n    onClose,\n    onCreateStep,\n    stepData,\n    setStepData,\n    isEditStepName,\n    stepName,\n    setStepName,\n    editType,\n    setEditType,\n    editDescription,\n    setDescription,\n    setTemplateSelectionPopup\n  } = props;\n  const {\n    setSelectedTemplate,\n    setBannerPopup,\n    setSelectedTemplateTour,\n    selectedTemplateTour,\n    selectedTemplate,\n    steps,\n    setSelectedStepTypeHotspot,\n    selectedStepTypeHotspot,\n    setBposition,\n    currentStep\n  } = useDrawerStore(state => state);\n  if (!isOpen) return null;\n  const handleStepTypeChange = e => {\n    const selectedType = e.target.value;\n    setStepData(prevData => ({\n      ...prevData,\n      type: selectedType\n    }));\n    setEditType(selectedType); // update editType state for future reference\n  };\n  const handleCreate = () => {\n    const updatedStepData = {\n      ...stepData,\n      type: (stepData === null || stepData === void 0 ? void 0 : stepData.type) || \"Announcement\"\n    };\n\n    // If creating a NEW Banner step in a Tour, set default position to Cover Top\n    if (updatedStepData.type === \"Banner\" && selectedTemplate === \"Tour\") {\n      // Only set default position for new banners\n      setBposition(\"Cover Top\");\n    }\n    onCreateStep(updatedStepData);\n    onClose();\n    setStepData(\"\");\n  };\n  let isDuplicateStep = false;\n  if (isEditStepName) {\n    const originalStepName = stepData.stepNumber;\n    isDuplicateStep = steps.some(step => step.name === stepName && step.name !== originalStepName);\n  } else {\n    isDuplicateStep = steps.some(step => step.name === stepName);\n  }\n  const stepNameTrimmed = stepName ? stepName.trim() : '';\n  const descriptionTrimmed = stepData.description ? stepData.description.replace(/\\s+/g, '') : editDescription ? editDescription.replace(/\\s+/g, '') : '';\n  const isCreateButtonDisabled = !stepName || stepNameTrimmed.length < 3 || stepNameTrimmed.length > 50 || isDuplicateStep || descriptionTrimmed.length > 50 || stepNameTrimmed.length > 20;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '195px',\n      backgroundColor: '#fff',\n      borderRadius: '6px',\n      boxShadow: '0px 4px 14px 0px #00000026',\n      border: '1px solid #e0e0e0',\n      zIndex: 1000,\n      height: 'auto',\n      overflow: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '10px 12px'\n        // width: '171px',\n        // maxHeight: '265px',    \n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'start',\n            marginBottom: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#616365',\n              lineHeight: '18px'\n            },\n            children: translate(\"Step Name\", {\n              defaultValue: \"Step Name\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `step-input ${isDuplicateStep ? 'qadpt-stbdr' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: stepName,\n            onChange: e => {\n              const newStepName = e.target.value;\n              //setStepData({ ...stepData, stepNumber: newStepName });\n              setStepName(newStepName);\n            },\n            onFocus: e => e.target.style.border = '1px solid #a8a8a8',\n            onBlur: e => e.target.style.border = '1px solid #a8a8a8',\n            style: {\n              border: isDuplicateStep || stepNameTrimmed.length > 20 ? '1px solid red' : '1px solid #a8a8a8'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 9\n        }, this), isDuplicateStep && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 5\n          }, this), translate(\"Step name should be unique\", {\n            defaultValue: \"Step name should be unique\"\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 3\n        }, this), stepName && stepNameTrimmed.length < 3 && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 3\n          }, this), translate(\"Guide name must be at least 3 characters.\", {\n            defaultValue: \"Guide name must be at least 3 characters.\"\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 3\n        }, this), stepName && stepNameTrimmed.length > 20 && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 3\n          }, this), translate(\"Step name should not exceed 20 characters.\", {\n            defaultValue: \"Step name should not exceed 20 characters.\"\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), selectedTemplate === \"Tour\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'start',\n            marginBottom: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#616365',\n              lineHeight: '18px'\n            },\n            children: translate(\"Step Type:\", {\n              defaultValue: \"Step Type:\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: stepData.type || (selectedStepTypeHotspot === true ? \"Hotspot\" : (_steps = steps[currentStep - 1]) === null || _steps === void 0 ? void 0 : _steps.stepType),\n            disabled: isEditStepName,\n            onOpen: e => {\n              setTemplateSelectionPopup(true);\n            },\n            onChange: e => {\n              setStepData({\n                ...stepData,\n                type: e.target.value\n              });\n              setEditType(e.target.value);\n              // If Banner is selected in a Tour, ensure position is set to Cover Top\n              if (e.target.value === \"Banner\" && selectedTemplate === \"Tour\") {\n                setBposition(\"Cover Top\");\n              }\n            },\n            onClose: e => {\n              setTemplateSelectionPopup(false);\n            },\n            displayEmpty: true,\n            MenuProps: {\n              sx: {\n                zIndex: 9999999\n              },\n              PopoverClasses: {\n                root: 'qadpt-turstp'\n              }\n            },\n            sx: {\n              width: \"171px\",\n              padding: \"6px 10px\",\n              borderRadius: \"6px\",\n              fontSize: \"14px\",\n              height: \"30px\",\n              color: \"#000\",\n              background: \"#fff\",\n              outline: \"none\",\n              textAlign: \"left\",\n              minWidth: \"100%\",\n              \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                borderColor: \"#a8a8a8\"\n              },\n              // Prevents color change on hover\n              \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                borderColor: \"#a8a8a8\"\n              },\n              // Prevents focus color change\n              \"& .MuiSelect-select\": {\n                paddingLeft: \"0 !important\"\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Announcement\",\n              children: translate(\"Announcement\", {\n                defaultValue: \"Announcement\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Banner\",\n              children: translate(\"Banner\", {\n                defaultValue: \"Banner\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Tooltip\",\n              children: translate(\"Tooltip\", {\n                defaultValue: \"Tooltip\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), (selectedStepTypeHotspot === true || editType === \"Hotspot\") && /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Hotspot\",\n              children: translate(\"Hotspot\", {\n                defaultValue: \"Hotspot\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 82\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'start',\n            marginBottom: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#616365',\n              lineHeight: '18px'\n            },\n            children: [translate(\"Description\", {\n              defaultValue: \"Description\"\n            }), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `step-input ${descriptionTrimmed.length > 50 ? 'qadpt-stbdr' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            placeholder: !stepData.description && !editDescription ? translate(\"write description\", {\n              defaultValue: \"write description\"\n            }) : \"\",\n            value: stepData.description || editDescription || \"\",\n            onChange: e => {\n              setStepData({\n                ...stepData,\n                description: e.target.value\n              });\n              setDescription(e.target.value);\n            },\n            style: {\n              width: \"-webkit-fill-available\",\n              padding: \"6px 10px\",\n              borderRadius: \"6px\",\n              background: \"#fff\",\n              outline: \"none\",\n              textAlign: \"left\",\n              minHeight: \"60px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 5\n          }, this), descriptionTrimmed.length > 50 && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 9\n            }, this), translate(\"Description must be a maximum of 50 characters.\", {\n              defaultValue: \"Description must be a maximum of 50 characters.\"\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"0 10px 10px 10px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreate,\n        disabled: isCreateButtonDisabled,\n        style: {\n          width: '100%',\n          padding: '7px 10px',\n          backgroundColor: \"var(--primarycolor)\",\n          // Change color when disabled\n          opacity: isCreateButtonDisabled ? \"0.5\" : \"1\",\n          color: '#fff',\n          border: 'none',\n          borderRadius: '7px',\n          cursor: 'pointer',\n          display: \"block\"\n        },\n        children: isEditStepName ? translate(\"Update\", {\n          defaultValue: \"Update\"\n        }) : translate(\"Create\", {\n          defaultValue: \"Create\"\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(StepCreationPopup, \"UWWJq9MTDASUPxp4qP4sqwJqf4M=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = StepCreationPopup;\nexport default StepCreationPopup;\nvar _c;\n$RefreshReg$(_c, \"StepCreationPopup\");", "map": {"version": 3, "names": ["React", "useDrawerStore", "Select", "MenuItem", "Typography", "warning", "useTranslation", "jsxDEV", "_jsxDEV", "StepCreationPopup", "props", "_s", "_steps", "t", "translate", "isOpen", "onClose", "onCreateStep", "stepData", "setStepData", "isEditStepName", "<PERSON><PERSON><PERSON>", "setStepName", "editType", "setEditType", "editDescription", "setDescription", "setTemplateSelectionPopup", "setSelectedTemplate", "setBannerPopup", "setSelectedTemplateTour", "selectedTemplateTour", "selectedTemplate", "steps", "setSelectedStepTypeHotspot", "selectedStepTypeHotspot", "setBposition", "currentStep", "state", "handleStepTypeChange", "e", "selectedType", "target", "value", "prevData", "type", "handleCreate", "updatedStepData", "isDuplicateStep", "originalStepName", "<PERSON><PERSON><PERSON><PERSON>", "some", "step", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim", "descriptionTrimmed", "description", "replace", "isCreateButtonDisabled", "length", "style", "width", "backgroundColor", "borderRadius", "boxShadow", "border", "zIndex", "height", "overflow", "children", "padding", "textAlign", "marginBottom", "color", "lineHeight", "defaultValue", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onChange", "newStepName", "onFocus", "onBlur", "fontSize", "top", "left", "display", "alignItems", "marginRight", "dangerouslySetInnerHTML", "__html", "stepType", "disabled", "onOpen", "displayEmpty", "MenuProps", "sx", "PopoverClasses", "root", "background", "outline", "min<PERSON><PERSON><PERSON>", "borderColor", "paddingLeft", "placeholder", "minHeight", "onClick", "opacity", "cursor", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/tours/stepPopup.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport { Select, MenuItem, Typography } from \"@mui/material\";\r\nimport { ForkLeft } from '@mui/icons-material';\r\nimport {warning} from \"../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst StepCreationPopup: React.FC<{ isOpen: boolean; onClose: () => void; onCreateStep:any,stepData:any,setStepData:any,isEditStepName:any,stepName:any,setStepName:any,editType:any,setEditType:any,editDescription:any,setDescription:any,setTemplateSelectionPopup:any}> = (props) => {    \r\n  const { t: translate } = useTranslation();\r\n  const {\r\n    isOpen, onClose, onCreateStep, stepData, setStepData, isEditStepName, stepName, setStepName, editType, setEditType, editDescription, setDescription, setTemplateSelectionPopup\r\n  } = props;\r\n  const {\r\n        setSelectedTemplate,\r\n        setBannerPopup,\r\n        setSelectedTemplateTour,\r\n  selectedTemplateTour,\r\n  selectedTemplate,\r\n  steps,\r\n  setSelectedStepTypeHotspot,\r\n  selectedStepTypeHotspot,\r\n    setBposition,\r\n  currentStep,\r\n    } = useDrawerStore((state: DrawerState) => state);\r\n\r\n  if (!isOpen) return null;\r\n  const handleStepTypeChange = (e: React.ChangeEvent<{ value: unknown }>) => {\r\n    const selectedType = e.target.value;\r\n    setStepData((prevData: any) => ({\r\n      ...prevData,\r\n      type: selectedType,\r\n    }));\r\n    setEditType(selectedType); // update editType state for future reference\r\n  };\r\n  const handleCreate = () => {\r\n    const updatedStepData =\r\n    {\r\n      ...stepData,\r\n      type: stepData?.type||\"Announcement\",\r\n      \r\n      }\r\n\r\n    // If creating a NEW Banner step in a Tour, set default position to Cover Top\r\n    if (updatedStepData.type === \"Banner\" && selectedTemplate === \"Tour\") {\r\n      // Only set default position for new banners\r\n      setBposition(\"Cover Top\");\r\n    }\r\n\r\n    onCreateStep(updatedStepData);\r\n      onClose();\r\n      setStepData(\"\");\r\n  };\r\n  let isDuplicateStep = false;\r\n\r\n\r\n  if (isEditStepName) {\r\n    \r\n    const originalStepName = stepData.stepNumber;\r\n    \r\n    isDuplicateStep = steps.some(step => \r\n      step.name === stepName && step.name !== originalStepName\r\n    );\r\n  } else {\r\n    isDuplicateStep = steps.some(step => step.name === stepName);\r\n    }\r\n\r\n  const stepNameTrimmed = stepName ? stepName.trim() : '';\r\n  const descriptionTrimmed = stepData.description ? stepData.description.replace(/\\s+/g, '') : editDescription ? editDescription.replace(/\\s+/g, '') : '';\r\n  const isCreateButtonDisabled =\r\n    !stepName || stepNameTrimmed.length < 3 || stepNameTrimmed.length > 50 || isDuplicateStep || descriptionTrimmed.length > 50 || stepNameTrimmed.length > 20;\r\n  return (\r\n    <div style={{\r\n      width: '195px',\r\n      backgroundColor: '#fff',\r\n      borderRadius: '6px',\r\n      boxShadow: '0px 4px 14px 0px #00000026',\r\n      border: '1px solid #e0e0e0',\r\n      zIndex: 1000,\r\n      height: 'auto',\r\n      overflow:'auto'\r\n    }}>\r\n      <div style={{\r\n              padding: '10px 12px',\r\n        // width: '171px',\r\n        // maxHeight: '265px',    \r\n      }}>\r\n        <div>\r\n        <div style={{\r\n          textAlign: 'start',\r\n          marginBottom:'4px'\r\n        }}>\r\n          <span style={{\r\n            color: '#616365',\r\n            lineHeight: '18px'  \r\n            }}>{translate(\"Step Name\", { defaultValue: \"Step Name\" })}</span>\r\n        </div>\r\n\r\n        <div className={`step-input ${isDuplicateStep ? 'qadpt-stbdr' : ''}`}>\r\n          <input\r\n            type=\"text\"\r\n            value={stepName}\r\n            onChange={(e) => {\r\n              const newStepName = e.target.value;\r\n              //setStepData({ ...stepData, stepNumber: newStepName });\r\n              setStepName(newStepName);\r\n            }}\r\n            onFocus={(e) => e.target.style.border = '1px solid #a8a8a8'}\r\n            onBlur={(e) => e.target.style.border = '1px solid #a8a8a8'}\r\n            style={{\r\n              border: isDuplicateStep || stepNameTrimmed.length > 20 ? '1px solid red' : '1px solid #a8a8a8'\r\n            }}\r\n          />\r\n        </div>\r\n\r\n{/* Error message for duplicate step name */}\r\n{isDuplicateStep && (\r\n  <Typography\r\n    style={{\r\n      fontSize: \"12px\",\r\n      color: \"#e9a971\",\r\n      textAlign: \"left\",\r\n      top: \"100%\",\r\n      left: 0,\r\n      marginBottom: \"5px\",\r\n      display: \"flex\",\r\n    }}\r\n  >\r\n    <span\r\n      style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n              {translate(\"Step name should be unique\", { defaultValue: \"Step name should be unique\" })}\r\n  </Typography>\r\n)}\r\n\r\n        {stepName && stepNameTrimmed.length < 3 && (\r\n  <Typography\r\n  style={{\r\n    fontSize: \"12px\",\r\n    color: \"#e9a971\",\r\n    textAlign: \"left\",\r\n    top: \"100%\",\r\n    left: 0,\r\n    marginBottom: \"5px\",\r\n    display: \"flex\",\r\n  }}\r\n>\r\n  <span\r\n    style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n    dangerouslySetInnerHTML={{ __html: warning }}\r\n  />\r\n              {translate(\"Guide name must be at least 3 characters.\", { defaultValue: \"Guide name must be at least 3 characters.\" })}\r\n            </Typography>\r\n          )}  \r\n          {stepName && stepNameTrimmed.length > 20 && (\r\n\r\n  <Typography\r\n  style={{\r\n    fontSize: \"12px\",\r\n    color: \"#e9a971\",\r\n    textAlign: \"left\",\r\n    top: \"100%\",\r\n    left: 0,\r\n    marginBottom: \"5px\",\r\n    display: \"flex\",\r\n  }}\r\n>\r\n  <span\r\n    style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n    dangerouslySetInnerHTML={{ __html: warning }}\r\n  />\r\n              {translate(\"Step name should not exceed 20 characters.\", { defaultValue: \"Step name should not exceed 20 characters.\" })}\r\n  </Typography>\r\n)}\r\n      </div>\r\n        {selectedTemplate === \"Tour\" && (\r\n\r\n\r\n          <div style={{\r\n            marginBottom: '12px',\r\n          }}>\r\n            <div style={{\r\n              textAlign: 'start',\r\n              marginBottom: '4px'\r\n            }}>\r\n              <span style={{\r\n                color: '#616365',\r\n                lineHeight: '18px'\r\n              }}>{translate(\"Step Type:\", { defaultValue: \"Step Type:\" })}</span>\r\n            </div>\r\n       \r\n            \r\n            <div>\r\n              <Select\r\n               value={stepData.type || (selectedStepTypeHotspot === true ? \"Hotspot\" : steps[currentStep-1]?.stepType)}\r\n                disabled={isEditStepName}\r\n                onOpen={(e) => {\r\n                  setTemplateSelectionPopup(true);\r\n                }}\r\n                onChange={(e) => {\r\n                  setStepData({ ...stepData, type: e.target.value });\r\n                  setEditType(e.target.value);\r\n                  // If Banner is selected in a Tour, ensure position is set to Cover Top\r\n                  if (e.target.value === \"Banner\" && selectedTemplate === \"Tour\") {\r\n                    setBposition(\"Cover Top\");\r\n                  }\r\n                }\r\n                }\r\n                onClose={(e) => {\r\n                  setTemplateSelectionPopup(false);\r\n                }}\r\n                displayEmpty\r\n                MenuProps={{\r\n                  sx: {\r\n                    zIndex: 9999999, \r\n                  },\r\n                  PopoverClasses: {\r\n                    root: 'qadpt-turstp', \r\n                  },\r\n                }}\r\n                sx={{\r\n                  width: \"171px\",\r\n                  padding: \"6px 10px\",\r\n                  borderRadius: \"6px\",\r\n                  fontSize: \"14px\",\r\n                  height: \"30px\",\r\n                  color: \"#000\",\r\n                  background: \"#fff\",\r\n                  outline: \"none\",\r\n                  textAlign: \"left\",\r\n                  minWidth: \"100%\",\r\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents color change on hover\r\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents focus color change\r\n                  \"& .MuiSelect-select\": { paddingLeft: \"0 !important\" },\r\n                 \r\n                }}\r\n              >\r\n                <MenuItem value=\"Announcement\">{translate(\"Announcement\", { defaultValue: \"Announcement\" })}</MenuItem>\r\n                <MenuItem value=\"Banner\">{translate(\"Banner\", { defaultValue: \"Banner\" })}</MenuItem>\r\n                <MenuItem value=\"Tooltip\">{translate(\"Tooltip\", { defaultValue: \"Tooltip\" })}</MenuItem>\r\n                {(selectedStepTypeHotspot === true || editType === \"Hotspot\") && <MenuItem value=\"Hotspot\">{translate(\"Hotspot\", { defaultValue: \"Hotspot\" })}</MenuItem>}\r\n              </Select>\r\n            </div>\r\n          \r\n\r\n          </div>\r\n        )}\r\n\r\n<div style={{ marginBottom: '12px' }}>\r\n  <div style={{ textAlign: 'start', marginBottom: '4px' }}>\r\n            <span style={{ color: '#616365', lineHeight: '18px' }}>{translate(\"Description\", { defaultValue: \"Description\" })}:</span>\r\n  </div>\r\n\r\n  <div className={`step-input ${descriptionTrimmed.length > 50 ? 'qadpt-stbdr' : ''}`}>\r\n    <textarea\r\n      placeholder={\r\n                (!stepData.description && !editDescription) ? translate(\"write description\", { defaultValue: \"write description\" }) : \"\"\r\n      }\r\n      value={stepData.description || editDescription || \"\"}\r\n      onChange={(e) => {\r\n        setStepData({ ...stepData, description: e.target.value });\r\n        setDescription(e.target.value);\r\n      }}\r\n      style={{\r\n        width: \"-webkit-fill-available\",\r\n        padding: \"6px 10px\",\r\n        borderRadius: \"6px\",\r\n        background: \"#fff\",\r\n        outline: \"none\",\r\n        textAlign: \"left\",\r\n        minHeight: \"60px\",\r\n      }}\r\n    />\r\n\r\n    {descriptionTrimmed.length > 50 && (\r\n      <Typography\r\n        style={{\r\n          fontSize: \"12px\",\r\n          color: \"#e9a971\",\r\n          textAlign: \"left\",\r\n          top: \"100%\",\r\n          left: 0,\r\n          marginBottom: \"5px\",\r\n          display: \"flex\",\r\n        }}\r\n      >\r\n        <span\r\n          style={{\r\n            display: \"flex\",\r\n            fontSize: \"12px\",\r\n            alignItems: \"center\",\r\n            marginRight: \"4px\"\r\n          }}\r\n          dangerouslySetInnerHTML={{ __html: warning }}\r\n        />\r\n                {translate(\"Description must be a maximum of 50 characters.\", { defaultValue: \"Description must be a maximum of 50 characters.\" })}\r\n      </Typography>\r\n    )}\r\n  </div>\r\n</div>\r\n\r\n       \r\n      </div>\r\n      <div style={{padding: \"0 10px 10px 10px\"}}>\r\n      <button\r\n          onClick={handleCreate}\r\n          disabled={isCreateButtonDisabled}\r\n          style={{\r\n            width: '100%',\r\n            padding: '7px 10px',\r\n            backgroundColor: \"var(--primarycolor)\", // Change color when disabled\r\n            opacity:isCreateButtonDisabled?\"0.5\":\"1\",\r\n            color: '#fff',\r\n            border: 'none',\r\n            borderRadius: '7px',\r\n            cursor: 'pointer',\r\n            display:\"block\",\r\n          }}\r\n        >\r\n          {isEditStepName ? translate(\"Update\", { defaultValue: \"Update\" }) : translate(\"Create\", { defaultValue: \"Create\" })}\r\n        </button>\r\n        </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StepCreationPopup;\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAA+B,OAAO;AAClD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE,SAASC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AAE5D,SAAQC,OAAO,QAAO,0BAA0B;AAChD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,iBAAqQ,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAAA,IAAAC,MAAA;EACvR,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGR,cAAc,CAAC,CAAC;EACzC,MAAM;IACJS,MAAM;IAAEC,OAAO;IAAEC,YAAY;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,cAAc;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EACvJ,CAAC,GAAGjB,KAAK;EACT,MAAM;IACAkB,mBAAmB;IACnBC,cAAc;IACdC,uBAAuB;IAC7BC,oBAAoB;IACpBC,gBAAgB;IAChBC,KAAK;IACLC,0BAA0B;IAC1BC,uBAAuB;IACrBC,YAAY;IACdC;EACE,CAAC,GAAGpC,cAAc,CAAEqC,KAAkB,IAAKA,KAAK,CAAC;EAEnD,IAAI,CAACvB,MAAM,EAAE,OAAO,IAAI;EACxB,MAAMwB,oBAAoB,GAAIC,CAAwC,IAAK;IACzE,MAAMC,YAAY,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACnCxB,WAAW,CAAEyB,QAAa,KAAM;MAC9B,GAAGA,QAAQ;MACXC,IAAI,EAAEJ;IACR,CAAC,CAAC,CAAC;IACHjB,WAAW,CAACiB,YAAY,CAAC,CAAC,CAAC;EAC7B,CAAC;EACD,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,eAAe,GACrB;MACE,GAAG7B,QAAQ;MACX2B,IAAI,EAAE,CAAA3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,IAAI,KAAE;IAEtB,CAAC;;IAEH;IACA,IAAIE,eAAe,CAACF,IAAI,KAAK,QAAQ,IAAIb,gBAAgB,KAAK,MAAM,EAAE;MACpE;MACAI,YAAY,CAAC,WAAW,CAAC;IAC3B;IAEAnB,YAAY,CAAC8B,eAAe,CAAC;IAC3B/B,OAAO,CAAC,CAAC;IACTG,WAAW,CAAC,EAAE,CAAC;EACnB,CAAC;EACD,IAAI6B,eAAe,GAAG,KAAK;EAG3B,IAAI5B,cAAc,EAAE;IAElB,MAAM6B,gBAAgB,GAAG/B,QAAQ,CAACgC,UAAU;IAE5CF,eAAe,GAAGf,KAAK,CAACkB,IAAI,CAACC,IAAI,IAC/BA,IAAI,CAACC,IAAI,KAAKhC,QAAQ,IAAI+B,IAAI,CAACC,IAAI,KAAKJ,gBAC1C,CAAC;EACH,CAAC,MAAM;IACLD,eAAe,GAAGf,KAAK,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKhC,QAAQ,CAAC;EAC5D;EAEF,MAAMiC,eAAe,GAAGjC,QAAQ,GAAGA,QAAQ,CAACkC,IAAI,CAAC,CAAC,GAAG,EAAE;EACvD,MAAMC,kBAAkB,GAAGtC,QAAQ,CAACuC,WAAW,GAAGvC,QAAQ,CAACuC,WAAW,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAGjC,eAAe,GAAGA,eAAe,CAACiC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE;EACvJ,MAAMC,sBAAsB,GAC1B,CAACtC,QAAQ,IAAIiC,eAAe,CAACM,MAAM,GAAG,CAAC,IAAIN,eAAe,CAACM,MAAM,GAAG,EAAE,IAAIZ,eAAe,IAAIQ,kBAAkB,CAACI,MAAM,GAAG,EAAE,IAAIN,eAAe,CAACM,MAAM,GAAG,EAAE;EAC5J,oBACEpD,OAAA;IAAKqD,KAAK,EAAE;MACVC,KAAK,EAAE,OAAO;MACdC,eAAe,EAAE,MAAM;MACvBC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,4BAA4B;MACvCC,MAAM,EAAE,mBAAmB;MAC3BC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAC;IACX,CAAE;IAAAC,QAAA,gBACA9D,OAAA;MAAKqD,KAAK,EAAE;QACJU,OAAO,EAAE;QACf;QACA;MACF,CAAE;MAAAD,QAAA,gBACA9D,OAAA;QAAA8D,QAAA,gBACA9D,OAAA;UAAKqD,KAAK,EAAE;YACVW,SAAS,EAAE,OAAO;YAClBC,YAAY,EAAC;UACf,CAAE;UAAAH,QAAA,eACA9D,OAAA;YAAMqD,KAAK,EAAE;cACXa,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE;YACZ,CAAE;YAAAL,QAAA,EAAExD,SAAS,CAAC,WAAW,EAAE;cAAE8D,YAAY,EAAE;YAAY,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAE,cAAcjC,eAAe,GAAG,aAAa,GAAG,EAAE,EAAG;UAAAsB,QAAA,eACnE9D,OAAA;YACEqC,IAAI,EAAC,MAAM;YACXF,KAAK,EAAEtB,QAAS;YAChB6D,QAAQ,EAAG1C,CAAC,IAAK;cACf,MAAM2C,WAAW,GAAG3C,CAAC,CAACE,MAAM,CAACC,KAAK;cAClC;cACArB,WAAW,CAAC6D,WAAW,CAAC;YAC1B,CAAE;YACFC,OAAO,EAAG5C,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmB,KAAK,CAACK,MAAM,GAAG,mBAAoB;YAC5DmB,MAAM,EAAG7C,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmB,KAAK,CAACK,MAAM,GAAG,mBAAoB;YAC3DL,KAAK,EAAE;cACLK,MAAM,EAAElB,eAAe,IAAIM,eAAe,CAACM,MAAM,GAAG,EAAE,GAAG,eAAe,GAAG;YAC7E;UAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGbhC,eAAe,iBACdxC,OAAA,CAACJ,UAAU;UACTyD,KAAK,EAAE;YACLyB,QAAQ,EAAE,MAAM;YAChBZ,KAAK,EAAE,SAAS;YAChBF,SAAS,EAAE,MAAM;YACjBe,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPf,YAAY,EAAE,KAAK;YACnBgB,OAAO,EAAE;UACX,CAAE;UAAAnB,QAAA,gBAEF9D,OAAA;YACEqD,KAAK,EAAE;cAAE4B,OAAO,EAAE,MAAM;cAAEH,QAAQ,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAEC,WAAW,EAAE;YAAM,CAAE;YACvFC,uBAAuB,EAAE;cAAEC,MAAM,EAAExF;YAAQ;UAAE;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EACSlE,SAAS,CAAC,4BAA4B,EAAE;YAAE8D,YAAY,EAAE;UAA6B,CAAC,CAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CACb,EAEQ3D,QAAQ,IAAIiC,eAAe,CAACM,MAAM,GAAG,CAAC,iBAC7CpD,OAAA,CAACJ,UAAU;UACXyD,KAAK,EAAE;YACLyB,QAAQ,EAAE,MAAM;YAChBZ,KAAK,EAAE,SAAS;YAChBF,SAAS,EAAE,MAAM;YACjBe,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPf,YAAY,EAAE,KAAK;YACnBgB,OAAO,EAAE;UACX,CAAE;UAAAnB,QAAA,gBAEF9D,OAAA;YACEqD,KAAK,EAAE;cAAE4B,OAAO,EAAE,MAAM;cAAEH,QAAQ,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAEC,WAAW,EAAE;YAAM,CAAE;YACvFC,uBAAuB,EAAE;cAAEC,MAAM,EAAExF;YAAQ;UAAE;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EACWlE,SAAS,CAAC,2CAA2C,EAAE;YAAE8D,YAAY,EAAE;UAA4C,CAAC,CAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CACb,EACA3D,QAAQ,IAAIiC,eAAe,CAACM,MAAM,GAAG,EAAE,iBAEhDpD,OAAA,CAACJ,UAAU;UACXyD,KAAK,EAAE;YACLyB,QAAQ,EAAE,MAAM;YAChBZ,KAAK,EAAE,SAAS;YAChBF,SAAS,EAAE,MAAM;YACjBe,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPf,YAAY,EAAE,KAAK;YACnBgB,OAAO,EAAE;UACX,CAAE;UAAAnB,QAAA,gBAEF9D,OAAA;YACEqD,KAAK,EAAE;cAAE4B,OAAO,EAAE,MAAM;cAAEH,QAAQ,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAEC,WAAW,EAAE;YAAM,CAAE;YACvFC,uBAAuB,EAAE;cAAEC,MAAM,EAAExF;YAAQ;UAAE;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EACWlE,SAAS,CAAC,4CAA4C,EAAE;YAAE8D,YAAY,EAAE;UAA6C,CAAC,CAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,EACHhD,gBAAgB,KAAK,MAAM,iBAG1BxB,OAAA;QAAKqD,KAAK,EAAE;UACVY,YAAY,EAAE;QAChB,CAAE;QAAAH,QAAA,gBACA9D,OAAA;UAAKqD,KAAK,EAAE;YACVW,SAAS,EAAE,OAAO;YAClBC,YAAY,EAAE;UAChB,CAAE;UAAAH,QAAA,eACA9D,OAAA;YAAMqD,KAAK,EAAE;cACXa,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAL,QAAA,EAAExD,SAAS,CAAC,YAAY,EAAE;cAAE8D,YAAY,EAAE;YAAa,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAGNxE,OAAA;UAAA8D,QAAA,eACE9D,OAAA,CAACN,MAAM;YACNyC,KAAK,EAAEzB,QAAQ,CAAC2B,IAAI,KAAKV,uBAAuB,KAAK,IAAI,GAAG,SAAS,IAAAvB,MAAA,GAAGqB,KAAK,CAACI,WAAW,GAAC,CAAC,CAAC,cAAAzB,MAAA,uBAApBA,MAAA,CAAsBkF,QAAQ,CAAE;YACvGC,QAAQ,EAAE3E,cAAe;YACzB4E,MAAM,EAAGxD,CAAC,IAAK;cACbb,yBAAyB,CAAC,IAAI,CAAC;YACjC,CAAE;YACFuD,QAAQ,EAAG1C,CAAC,IAAK;cACfrB,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAE2B,IAAI,EAAEL,CAAC,CAACE,MAAM,CAACC;cAAM,CAAC,CAAC;cAClDnB,WAAW,CAACgB,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;cAC3B;cACA,IAAIH,CAAC,CAACE,MAAM,CAACC,KAAK,KAAK,QAAQ,IAAIX,gBAAgB,KAAK,MAAM,EAAE;gBAC9DI,YAAY,CAAC,WAAW,CAAC;cAC3B;YACF,CACC;YACDpB,OAAO,EAAGwB,CAAC,IAAK;cACdb,yBAAyB,CAAC,KAAK,CAAC;YAClC,CAAE;YACFsE,YAAY;YACZC,SAAS,EAAE;cACTC,EAAE,EAAE;gBACFhC,MAAM,EAAE;cACV,CAAC;cACDiC,cAAc,EAAE;gBACdC,IAAI,EAAE;cACR;YACF,CAAE;YACFF,EAAE,EAAE;cACFrC,KAAK,EAAE,OAAO;cACdS,OAAO,EAAE,UAAU;cACnBP,YAAY,EAAE,KAAK;cACnBsB,QAAQ,EAAE,MAAM;cAChBlB,MAAM,EAAE,MAAM;cACdM,KAAK,EAAE,MAAM;cACb4B,UAAU,EAAE,MAAM;cAClBC,OAAO,EAAE,MAAM;cACf/B,SAAS,EAAE,MAAM;cACjBgC,QAAQ,EAAE,MAAM;cAChB,0CAA0C,EAAE;gBAAEC,WAAW,EAAE;cAAU,CAAC;cAAE;cACxE,gDAAgD,EAAE;gBAAEA,WAAW,EAAE;cAAU,CAAC;cAAE;cAC9E,qBAAqB,EAAE;gBAAEC,WAAW,EAAE;cAAe;YAEvD,CAAE;YAAApC,QAAA,gBAEF9D,OAAA,CAACL,QAAQ;cAACwC,KAAK,EAAC,cAAc;cAAA2B,QAAA,EAAExD,SAAS,CAAC,cAAc,EAAE;gBAAE8D,YAAY,EAAE;cAAe,CAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvGxE,OAAA,CAACL,QAAQ;cAACwC,KAAK,EAAC,QAAQ;cAAA2B,QAAA,EAAExD,SAAS,CAAC,QAAQ,EAAE;gBAAE8D,YAAY,EAAE;cAAS,CAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrFxE,OAAA,CAACL,QAAQ;cAACwC,KAAK,EAAC,SAAS;cAAA2B,QAAA,EAAExD,SAAS,CAAC,SAAS,EAAE;gBAAE8D,YAAY,EAAE;cAAU,CAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACvF,CAAC7C,uBAAuB,KAAK,IAAI,IAAIZ,QAAQ,KAAK,SAAS,kBAAKf,OAAA,CAACL,QAAQ;cAACwC,KAAK,EAAC,SAAS;cAAA2B,QAAA,EAAExD,SAAS,CAAC,SAAS,EAAE;gBAAE8D,YAAY,EAAE;cAAU,CAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CACN,eAETxE,OAAA;QAAKqD,KAAK,EAAE;UAAEY,YAAY,EAAE;QAAO,CAAE;QAAAH,QAAA,gBACnC9D,OAAA;UAAKqD,KAAK,EAAE;YAAEW,SAAS,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAH,QAAA,eAC9C9D,OAAA;YAAMqD,KAAK,EAAE;cAAEa,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAL,QAAA,GAAExD,SAAS,CAAC,aAAa,EAAE;cAAE8D,YAAY,EAAE;YAAc,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/H,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAE,cAAczB,kBAAkB,CAACI,MAAM,GAAG,EAAE,GAAG,aAAa,GAAG,EAAE,EAAG;UAAAU,QAAA,gBAClF9D,OAAA;YACEmG,WAAW,EACA,CAACzF,QAAQ,CAACuC,WAAW,IAAI,CAAChC,eAAe,GAAIX,SAAS,CAAC,mBAAmB,EAAE;cAAE8D,YAAY,EAAE;YAAoB,CAAC,CAAC,GAAG,EAC/H;YACDjC,KAAK,EAAEzB,QAAQ,CAACuC,WAAW,IAAIhC,eAAe,IAAI,EAAG;YACrDyD,QAAQ,EAAG1C,CAAC,IAAK;cACfrB,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEuC,WAAW,EAAEjB,CAAC,CAACE,MAAM,CAACC;cAAM,CAAC,CAAC;cACzDjB,cAAc,CAACc,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;YAChC,CAAE;YACFkB,KAAK,EAAE;cACLC,KAAK,EAAE,wBAAwB;cAC/BS,OAAO,EAAE,UAAU;cACnBP,YAAY,EAAE,KAAK;cACnBsC,UAAU,EAAE,MAAM;cAClBC,OAAO,EAAE,MAAM;cACf/B,SAAS,EAAE,MAAM;cACjBoC,SAAS,EAAE;YACb;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEDxB,kBAAkB,CAACI,MAAM,GAAG,EAAE,iBAC7BpD,OAAA,CAACJ,UAAU;YACTyD,KAAK,EAAE;cACLyB,QAAQ,EAAE,MAAM;cAChBZ,KAAK,EAAE,SAAS;cAChBF,SAAS,EAAE,MAAM;cACjBe,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPf,YAAY,EAAE,KAAK;cACnBgB,OAAO,EAAE;YACX,CAAE;YAAAnB,QAAA,gBAEF9D,OAAA;cACEqD,KAAK,EAAE;gBACL4B,OAAO,EAAE,MAAM;gBACfH,QAAQ,EAAE,MAAM;gBAChBI,UAAU,EAAE,QAAQ;gBACpBC,WAAW,EAAE;cACf,CAAE;cACFC,uBAAuB,EAAE;gBAAEC,MAAM,EAAExF;cAAQ;YAAE;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,EACOlE,SAAS,CAAC,iDAAiD,EAAE;cAAE8D,YAAY,EAAE;YAAkD,CAAC,CAAC;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChI,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGK,CAAC,eACNxE,OAAA;MAAKqD,KAAK,EAAE;QAACU,OAAO,EAAE;MAAkB,CAAE;MAAAD,QAAA,eAC1C9D,OAAA;QACIqG,OAAO,EAAE/D,YAAa;QACtBiD,QAAQ,EAAEpC,sBAAuB;QACjCE,KAAK,EAAE;UACLC,KAAK,EAAE,MAAM;UACbS,OAAO,EAAE,UAAU;UACnBR,eAAe,EAAE,qBAAqB;UAAE;UACxC+C,OAAO,EAACnD,sBAAsB,GAAC,KAAK,GAAC,GAAG;UACxCe,KAAK,EAAE,MAAM;UACbR,MAAM,EAAE,MAAM;UACdF,YAAY,EAAE,KAAK;UACnB+C,MAAM,EAAE,SAAS;UACjBtB,OAAO,EAAC;QACV,CAAE;QAAAnB,QAAA,EAEDlD,cAAc,GAAGN,SAAS,CAAC,QAAQ,EAAE;UAAE8D,YAAY,EAAE;QAAS,CAAC,CAAC,GAAG9D,SAAS,CAAC,QAAQ,EAAE;UAAE8D,YAAY,EAAE;QAAS,CAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrE,EAAA,CA7TIF,iBAAqQ;EAAA,QAChPH,cAAc,EAejCL,cAAc;AAAA;AAAA+G,EAAA,GAhBhBvG,iBAAqQ;AA+T3Q,eAAeA,iBAAiB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}