{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\components\\\\Buttons.tsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useState } from \"react\";\nimport { Box, Button, Popover, IconButton, Tooltip } from \"@mui/material\";\nimport { ChromePicker } from \"react-color\";\nimport { deleteicon, copyicon, settingsicon } from \"../../../assets/icons/icons\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport ButtonSetting from \"./ButtonSetting\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ButtonSection = ({\n  items: buttonsContainer,\n  updatedGuideData,\n  isCloneDisabled\n}) => {\n  _s();\n  var _toolTipGuideMetaData7, _toolTipGuideMetaData8, _toolTipGuideMetaData9, _toolTipGuideMetaData10;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    // buttonsContainer,\n    tooltipBtnSettingAnchorEl: settingAnchorEl,\n    cloneTooltipButtonContainer: cloneButtonContainer,\n    updateButtonInTooltip: updateButton,\n    addNewButtonInTooltip: addNewButton,\n    deleteButtonInTooltip: deleteButton,\n    updateTooltipButtonInteraction,\n    updateTooltipButtonAction,\n    deleteTooltipButtonContainer: deleteButtonContainer,\n    updateTooltipBtnContainer: updateContainer,\n    setTooltipBtnSettingAnchorEl: setSettingAnchorEl,\n    currentStep,\n    setbtnidss,\n    getCurrentButtonInfo,\n    toolTipGuideMetaData,\n    highlightedButton,\n    setElementClick,\n    SetElementButtonClick\n  } = useDrawerStore(state => state);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\n  const [currentContainerId, setCurrentContainerId] = useState(\"\");\n  const [currentButtonId, setCurrentButtonId] = useState(\"\");\n  // Default button color\n  let clickTimeout;\n  const handleClick = (event, containerId, buttonId) => {\n    const target = event.currentTarget;\n    setAnchorEl(target);\n    setSettingAnchorEl({\n      containerId,\n      buttonId,\n      value: null\n    });\n\n    // Set current container and button IDs for reference\n    setCurrentContainerId(containerId);\n    setCurrentButtonId(buttonId);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n  const handleColorChange = color => {\n    // Update the backgroundColor in the container's style\n    updateContainer(settingAnchorEl.containerId, \"style\", {\n      backgroundColor: color.hex\n    });\n\n    // Also update the BackgroundColor property at the ButtonSection level\n    updateContainer(settingAnchorEl.containerId, \"BackgroundColor\", color.hex);\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const open = Boolean(anchorEl);\n  // const open = Boolean(anchorEl && !isEditingButton);\n  // const id = open ? \"button-popover\" : undefined;\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const handleEditButtonName = (containerId, buttonId, isEditing, value) => {\n    // clearTimeout(clickTimeout);\n    updateButton(containerId, buttonId, isEditing, value);\n  };\n  const handleChangeButton = (containerId, buttonId, value) => {\n    updateButton(containerId, buttonId, \"type\", value);\n    setAnchorEl(null);\n  };\n  const handleAddIconClick = containerId => {\n    addNewButton(containerId);\n  };\n  const shouldShowAddBtn = buttonsContainer.buttons.length;\n  const buttonInfo = useMemo(() => {\n    let result = null;\n    if (settingAnchorEl.buttonId) {\n      result = buttonsContainer.buttons.find(item => item.id === settingAnchorEl.buttonId);\n    }\n    return result;\n  }, [settingAnchorEl.buttonId, buttonsContainer.buttons]);\n\n  // console.log({ buttonInfo });\n\n  // setButtonId(currentButtonId);\n  // setCuntainerId(currentButtonId);\n  const handleDelteContainer = () => {\n    deleteButtonContainer(settingAnchorEl.containerId);\n    setElementClick(\"element\");\n    const updatedData = {\n      ...updatedGuideData\n    };\n    if (updatedData.GuideStep && updatedData.GuideStep[currentStep]) {\n      const stepData = {\n        ...updatedData.GuideStep[currentStep - 1]\n      };\n\n      // Ensure GotoNext exists before modifying ButtonId\n      if (stepData.Design && stepData.Design.GotoNext && stepData.Design.GotoNext.ButtonId !== undefined) {\n        stepData.Design = {\n          ...stepData.Design,\n          GotoNext: {\n            ...stepData.Design.GotoNext,\n            ButtonId: \"\",\n            NextStep: \"element\"\n          }\n        };\n      }\n      setbtnidss(\"\");\n      // Update the GuideStep array with modified step data\n      updatedData.GuideStep = [...updatedData.GuideStep];\n      updatedData.GuideStep[currentStep - 1] = stepData;\n      updatedGuideData = updatedData;\n    }\n    setbtnidss(\"\");\n    setAnchorEl(null);\n  };\n  const handleSettingIconClick = event => {\n    //current container and button IDs\n    const containerId = settingAnchorEl.containerId || currentContainerId;\n    const buttonId = settingAnchorEl.buttonId || currentButtonId;\n    setSettingAnchorEl({\n      containerId,\n      buttonId,\n      // @ts-ignore\n      value: event.currentTarget\n    });\n    handleClose();\n  };\n  const handleCloseSettingPopup = (_containerId, _buttonId) => {\n    // updateButtonAction(containerId, buttonId, {\n    // \tvalue: selectedActions,\n    // \ttargetURL: targetURL,\n    // \ttab: selectedTab,\n    // \tinteraction: null,\n    // });\n    // updateButtonInteraction(containerId, buttonId, selectedInteraction);\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n    setAnchorEl(null);\n  };\n\n  // Clear the design button settings if the deleted button was previously selected\n  const deletebuttonidindesign = (buttonid, deletebuttonid) => {\n    if (buttonid === deletebuttonid) {\n      var _updatedGuideData, _updatedGuideData$Gui;\n      const targetStep = {\n        ...((_updatedGuideData = updatedGuideData) === null || _updatedGuideData === void 0 ? void 0 : (_updatedGuideData$Gui = _updatedGuideData.GuideStep) === null || _updatedGuideData$Gui === void 0 ? void 0 : _updatedGuideData$Gui[currentStep - 1])\n      };\n      if (targetStep && targetStep.Design) {\n        targetStep.Design.GotoNext = {\n          ButtonId: \"\",\n          ButtonName: \"\",\n          ElementPath: \"\",\n          NextStep: \"element\"\n        };\n      }\n      updatedGuideData.GuideStep[currentStep - 1] = targetStep;\n    }\n  };\n  const handleApplyChanges = (tempColors, selectedActions, targetURL, selectedInteraction, currentButtonName, selectedTab) => {\n    // Get the container and button IDs\n    const {\n      containerId,\n      buttonId\n    } = settingAnchorEl;\n    // Update the button style - make sure we're passing the correct structure\n    updateButton(containerId, buttonId, \"style\", {\n      backgroundColor: tempColors.backgroundColor,\n      borderColor: tempColors.borderColor,\n      color: tempColors.color\n    });\n    updateTooltipButtonAction(containerId, buttonId, {\n      value: selectedActions,\n      targetURL: targetURL,\n      tab: selectedTab,\n      interaction: null\n    });\n    updateTooltipButtonInteraction(containerId, buttonId, selectedInteraction);\n    updateButton(containerId, buttonId, \"name\", currentButtonName);\n\n    // Clear selection\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n    setAnchorEl(null);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      component: \"div\",\n      id: buttonsContainer.id,\n      sx: {\n        height: \"60px\",\n        width: \"100%\",\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: \"5px\",\n        padding: \"0px\",\n        boxSizing: \"border-box\",\n        backgroundColor: buttonsContainer.style.backgroundColor,\n        justifyContent: \"center\"\n      }\n      // onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\n      // onMouseLeave={(e) => setCurrentContainerId(\"\")}\n      ,\n      children: [buttonsContainer.buttons.map(item => {\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"relative\",\n            display: \"flex\",\n            // flex: 1,\n            justifyContent: `center`\n            // \"&:hover .edit-icon\": { display: \"inline-flex\" },\n          },\n          onMouseLeave: () => {\n            setIsDeleteIcon(\"\");\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button\n          // contentEditable={item.isEditing}\n          , {\n            onMouseOver: e => {\n              if (item.isEditing === false && e.currentTarget.id === item.id) {\n                setIsDeleteIcon(item.id);\n              }\n            },\n            id: item.id,\n            variant: \"contained\",\n            sx: {\n              borderRadius: \"8px\",\n              transition: \"none\",\n              boxShadow: \"none !important\",\n              border: item.style.borderColor,\n              //border: `${item.type !== \"primary\" ? item.style.borderColor : \"none\"}`,\n              color: `${item.style.color}`,\n              textTransform: \"none\",\n              padding: \"4px 8px !important\",\n              lineHeight: \"var(--button-lineheight)\",\n              fontSize: \"14px !important\",\n              backgroundColor: item.style.backgroundColor,\n              width: \"fit-content\",\n              //boxShadow: \"none !important\", // Remove box shadow in normal state\n              \"&:hover\": {\n                backgroundColor: item.style.backgroundColor,\n                // Keep the same background color on hover\n                opacity: 0.9,\n                // Slightly reduce opacity on hover for visual feedback\n                boxShadow: \"none !important\" // Remove box shadow in hover state\n              }\n            },\n            onClick: e => handleClick(e, buttonsContainer.id, item.id) // Open popover when clicking the button\n            ,\n            children: translate(item.name, {\n              defaultValue: item.name\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 8\n          }, this), buttonsContainer.buttons.length > 1 && isDeleteIcon === item.id ? /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            className: \"del-icon\",\n            sx: {\n              position: \"absolute\",\n              top: \"-10px\",\n              right: \"-10px\",\n              backgroundColor: \"#fff\",\n              //boxShadow: \"none !important\", // Remove box shadow in normal state\n              // display: \"none\", // Initially hidden\n              boxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\n              zIndex: \"1\",\n              padding: \"3px !important\",\n              \"&:hover\": {\n                backgroundColor: \"#fff\",\n                boxShadow: \"none !important\"\n              },\n              span: {\n                height: \"14px\"\n              },\n              svg: {\n                width: \"14px\",\n                height: \"14px\",\n                path: {\n                  fill: \"#ff0000\"\n                }\n              }\n            },\n            onClick: e => {\n              var _toolTipGuideMetaData, _toolTipGuideMetaData2, _toolTipGuideMetaData3, _toolTipGuideMetaData4, _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n              e.stopPropagation();\n              deletebuttonidindesign(item.id, (_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.design) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : (_toolTipGuideMetaData3 = _toolTipGuideMetaData2.gotoNext) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : _toolTipGuideMetaData3.ButtonId);\n              deleteButton(item.id, buttonsContainer.id, (_toolTipGuideMetaData4 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : (_toolTipGuideMetaData5 = _toolTipGuideMetaData4.design) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.gotoNext) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.ButtonId);\n              setElementClick(\"element\");\n              // setButtonClick(false);\n              SetElementButtonClick(false);\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 9\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 7\n        }, this);\n      }), shouldShowAddBtn < 4 ? /*#__PURE__*/_jsxDEV(IconButton, {\n        sx: {\n          backgroundColor: \"#5F9EA0\",\n          cursor: \"pointer\",\n          zIndex: 1000,\n          padding: \"6px !important\",\n          boxShadow: \"none !important\",\n          // Remove box shadow in normal state\n          \"&:hover\": {\n            backgroundColor: \"#70afaf\",\n            boxShadow: \"none !important\" // Remove box shadow in hover state\n          }\n        }\n        // sx={sideAddButtonStyle}\n        ,\n        onClick: () => handleAddIconClick(buttonsContainer.id),\n        children: /*#__PURE__*/_jsxDEV(AddIcon, {\n          fontSize: \"small\",\n          sx: {\n            color: \"#fff\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 6\n      }, this) : null, /*#__PURE__*/_jsxDEV(Popover, {\n        id: \"button-toolbar\",\n        open: open\n        // anchorEl={anchorEl}\n        ,\n        onClose: handleClose,\n        anchorReference: \"anchorPosition\",\n        anchorPosition: {\n          top: (anchorEl === null || anchorEl === void 0 ? void 0 : anchorEl.getBoundingClientRect().top) || 0,\n          left: (anchorEl === null || anchorEl === void 0 ? void 0 : anchorEl.getBoundingClientRect().left) || 0\n        },\n        anchorOrigin: {\n          vertical: \"top\",\n          horizontal: \"left\"\n        },\n        transformOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"left\"\n        },\n        slotProps: {\n          root: {\n            // instead of writing sx on popover write here it also target to root and more clear\n            sx: {\n              zIndex: theme => theme.zIndex.tooltip + 1000\n            }\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"6px\",\n            padding: \"4px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleSettingIconClick,\n            sx: {\n              boxShadow: \"none !important\",\n              // Remove box shadow in normal state\n              \"&:hover\": {\n                boxShadow: \"none !important\" // Remove box shadow in hover state\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: settingsicon\n              },\n              style: {\n                width: \"20px\",\n                height: \"20px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            arrow: true,\n            title: \"Background Color\",\n            style: {\n              zIndex: 99999\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                backgroundColor: buttonsContainer.style.backgroundColor === \"#5f9ea0\" ? buttonsContainer.style.backgroundColor : \"#e0dbdb\",\n                width: \"20px\",\n                height: \"20px\",\n                borderRadius: \"50%\",\n                //border: `1px solid red`,\n                marginTop: \"-3px\"\n              },\n              component: \"div\",\n              role: \"button\",\n              onClick: handleBackgroundColorClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => {\n              cloneButtonContainer(settingAnchorEl.containerId);\n              setAnchorEl(null);\n            },\n            disabled: isCloneDisabled,\n            title: isCloneDisabled ? \"Maximum limit of 3 Button sections reached\" : \"Clone Section\",\n            sx: {\n              boxShadow: \"none !important\",\n              // Remove box shadow in normal state\n              \"&:hover\": {\n                boxShadow: \"none !important\" // Remove box shadow in hover state\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: copyicon\n              },\n              style: {\n                opacity: isCloneDisabled ? 0.5 : 1,\n                width: \"20px\",\n                height: \"20px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\"\n            // disabled={buttonsContainer.buttons.length === 1}\n            ,\n            onClick: handleDelteContainer,\n            disabled: ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData7 === void 0 ? void 0 : (_toolTipGuideMetaData8 = _toolTipGuideMetaData7.containers) === null || _toolTipGuideMetaData8 === void 0 ? void 0 : _toolTipGuideMetaData8.length) === 1,\n            sx: {\n              boxShadow: \"none !important\",\n              // Remove box shadow in normal state\n              \"&:hover\": {\n                boxShadow: \"none !important\" // Remove box shadow in hover state\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              },\n              style: {\n                opacity: ((_toolTipGuideMetaData9 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData9 === void 0 ? void 0 : (_toolTipGuideMetaData10 = _toolTipGuideMetaData9.containers) === null || _toolTipGuideMetaData10 === void 0 ? void 0 : _toolTipGuideMetaData10.length) === 1 ? 0.5 : 1,\n                pointerEvents: \"none\",\n                width: \"20px\",\n                height: \"24px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(ButtonSetting, {\n        handleCloseSettingPopup: handleCloseSettingPopup,\n        settingAnchorEl: settingAnchorEl,\n        buttonInfo: buttonInfo,\n        handleApplyChanges: handleApplyChanges,\n        updatedGuideData: updatedGuideData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      id: \"color-picker\",\n      slotProps: {\n        root: {\n          // instead of writing sx on popover write here it also target to root and more clear\n          sx: {\n            zIndex: theme => theme.zIndex.tooltip + 1000\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: buttonsContainer.style.backgroundColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ButtonSection, \"h9e8yRdQrTkpl4mQ0QFSESRx0lU=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ButtonSection;\nexport default ButtonSection;\nvar _c;\n$RefreshReg$(_c, \"ButtonSection\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "Box", "<PERSON><PERSON>", "Popover", "IconButton", "<PERSON><PERSON><PERSON>", "ChromePicker", "deleteicon", "copyicon", "settingsicon", "useDrawerStore", "AddIcon", "ButtonSetting", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ButtonSection", "items", "buttonsContainer", "updatedGuideData", "isCloneDisabled", "_s", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_toolTipGuideMetaData10", "t", "translate", "tooltipBtnSettingAnchorEl", "settingAnchorEl", "cloneTooltipButtonContainer", "cloneButtonContainer", "updateButtonInTooltip", "updateButton", "addNewButtonInTooltip", "addNewButton", "deleteButtonInTooltip", "deleteButton", "updateTooltipButtonInteraction", "updateTooltipButtonAction", "deleteTooltipButtonContainer", "deleteButtonContainer", "updateTooltipBtnContainer", "updateContainer", "setTooltipBtnSettingAnchorEl", "setSettingAnchorEl", "currentStep", "setbtnidss", "getCurrentButtonInfo", "toolTipGuideMetaData", "highlighted<PERSON><PERSON><PERSON>", "setElementClick", "SetElementButtonClick", "state", "anchorEl", "setAnchorEl", "colorPickerAnchorEl", "setColorPickerAnchorEl", "isDeleteIcon", "setIsDeleteIcon", "currentContainerId", "setCurrentContainerId", "currentButtonId", "setCurrentButtonId", "clickTimeout", "handleClick", "event", "containerId", "buttonId", "target", "currentTarget", "value", "handleClose", "handleBackgroundColorClick", "handleColorChange", "color", "backgroundColor", "hex", "handleCloseColorPicker", "open", "Boolean", "colorPickerOpen", "handleEditButtonName", "isEditing", "handleChangeButton", "handleAddIconClick", "shouldShowAddBtn", "buttons", "length", "buttonInfo", "result", "find", "item", "id", "handleDelteContainer", "updatedData", "GuideStep", "stepData", "Design", "GotoNext", "ButtonId", "undefined", "NextStep", "handleSettingIconClick", "handleCloseSettingPopup", "_containerId", "_buttonId", "deletebuttonidindesign", "buttonid", "deletebuttonid", "_updatedGuideData", "_updatedGuideData$Gui", "targetStep", "ButtonName", "<PERSON>ement<PERSON><PERSON>", "handleApplyChanges", "tempColors", "selectedActions", "targetURL", "selectedInteraction", "currentButtonName", "selectedTab", "borderColor", "tab", "interaction", "children", "component", "sx", "height", "width", "display", "alignItems", "gap", "padding", "boxSizing", "style", "justifyContent", "map", "position", "onMouseLeave", "onMouseOver", "e", "variant", "borderRadius", "transition", "boxShadow", "border", "textTransform", "lineHeight", "fontSize", "opacity", "onClick", "name", "defaultValue", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "className", "top", "right", "zIndex", "span", "svg", "path", "fill", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "stopPropagation", "design", "gotoNext", "dangerouslySetInnerHTML", "__html", "cursor", "onClose", "anchorReference", "anchorPosition", "getBoundingClientRect", "left", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "root", "theme", "tooltip", "arrow", "title", "marginTop", "role", "disabled", "containers", "pointerEvents", "onChange", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/Buttons.tsx"], "sourcesContent": ["import React, { use<PERSON>emo, useState } from \"react\";\r\nimport { Box, Button, Popover, Typo<PERSON>, <PERSON><PERSON>ield, IconButton, Tooltip } from \"@mui/material\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, { ButtonContainer, TButton } from \"../../../store/drawerStore\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport ButtonSetting from \"./ButtonSetting\";\r\nimport { useAsyncError } from \"react-router-dom\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ButtonSection: React.FC<{ items: ButtonContainer; updatedGuideData: any; isCloneDisabled?: boolean }> = ({\r\n\titems: buttonsContainer,\r\n\tupdatedGuideData,\r\n\tisCloneDisabled,\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\t// buttonsContainer,\r\n\t\ttooltipBtnSettingAnchorEl: settingAnchorEl,\r\n\t\tcloneTooltipButtonContainer: cloneButtonContainer,\r\n\t\tupdateButtonInTooltip: updateButton,\r\n\t\taddNewButtonInTooltip: addNewButton,\r\n\t\tdeleteButtonInTooltip: deleteButton,\r\n\t\tupdateTooltipButtonInteraction,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tdeleteTooltipButtonContainer: deleteButtonContainer,\r\n\t\tupdateTooltipBtnContainer: updateContainer,\r\n\t\tsetTooltipBtnSettingAnchorEl: setSettingAnchorEl,\r\n\t\tcurrentStep,\r\n\t\tsetbtnidss,\r\n\t\tgetCurrentButtonInfo,\r\n\t\ttoolTipGuideMetaData,\r\n\t\thighlightedButton,\r\n\t\tsetElementClick,\r\n\t\tSetElementButtonClick,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\r\n\tconst [currentContainerId, setCurrentContainerId] = useState(\"\");\r\n\tconst [currentButtonId, setCurrentButtonId] = useState(\"\");\r\n\t// Default button color\r\n\tlet clickTimeout: NodeJS.Timeout;\r\n\tconst handleClick = (event: React.MouseEvent<HTMLElement>, containerId: string, buttonId: string) => {\r\n\t\tconst target = event.currentTarget;\r\n\t\tsetAnchorEl(target);\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId,\r\n\t\t\tbuttonId,\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Set current container and button IDs for reference\r\n\t\tsetCurrentContainerId(containerId);\r\n\t\tsetCurrentButtonId(buttonId);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\t// Update the backgroundColor in the container's style\r\n\t\tupdateContainer(settingAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\r\n\t\t// Also update the BackgroundColor property at the ButtonSection level\r\n\t\tupdateContainer(settingAnchorEl.containerId, \"BackgroundColor\", color.hex);\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(anchorEl);\r\n\t// const open = Boolean(anchorEl && !isEditingButton);\r\n\t// const id = open ? \"button-popover\" : undefined;\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst handleEditButtonName = (\r\n\t\tcontainerId: string,\r\n\t\tbuttonId: string,\r\n\t\tisEditing: keyof TButton,\r\n\t\tvalue: TButton[keyof TButton]\r\n\t) => {\r\n\t\t// clearTimeout(clickTimeout);\r\n\t\tupdateButton(containerId, buttonId, isEditing, value);\r\n\t};\r\n\r\n\tconst handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {\r\n\t\tupdateButton(containerId, buttonId, \"type\", value);\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleAddIconClick = (containerId: string) => {\r\n\t\taddNewButton(containerId);\r\n\t};\r\n\r\n\tconst shouldShowAddBtn = buttonsContainer.buttons.length;\r\n\r\n\tconst buttonInfo = useMemo(() => {\r\n\t\tlet result = null;\r\n\t\tif (settingAnchorEl.buttonId) {\r\n\t\t\tresult = buttonsContainer.buttons.find((item: any) => item.id === settingAnchorEl.buttonId);\r\n\t\t}\r\n\t\treturn result;\r\n\t}, [settingAnchorEl.buttonId, buttonsContainer.buttons]);\r\n\r\n\t// console.log({ buttonInfo });\r\n\r\n\t// setButtonId(currentButtonId);\r\n\t// setCuntainerId(currentButtonId);\r\n\tconst handleDelteContainer = () => {\r\n\t\tdeleteButtonContainer(settingAnchorEl.containerId);\r\n\t\tsetElementClick(\"element\");\r\n\t\tconst updatedData = { ...updatedGuideData };\r\n\t\tif (updatedData.GuideStep && updatedData.GuideStep[currentStep]) {\r\n\t\t\tconst stepData = { ...updatedData.GuideStep[currentStep - 1] };\r\n\r\n\t\t\t// Ensure GotoNext exists before modifying ButtonId\r\n\t\t\tif (stepData.Design && stepData.Design.GotoNext && stepData.Design.GotoNext.ButtonId !== undefined) {\r\n\t\t\t\tstepData.Design = {\r\n\t\t\t\t\t...stepData.Design,\r\n\t\t\t\t\tGotoNext: {\r\n\t\t\t\t\t\t...stepData.Design.GotoNext,\r\n\t\t\t\t\t\tButtonId: \"\",\r\n\t\t\t\t\t\tNextStep: \"element\",\r\n\t\t\t\t\t},\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\tsetbtnidss(\"\");\r\n\t\t\t// Update the GuideStep array with modified step data\r\n\t\t\tupdatedData.GuideStep = [...updatedData.GuideStep];\r\n\t\t\tupdatedData.GuideStep[currentStep - 1] = stepData;\r\n\t\t\tupdatedGuideData = updatedData;\r\n\t\t}\r\n\r\n\t\tsetbtnidss(\"\");\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\t//current container and button IDs\r\n\t\tconst containerId = settingAnchorEl.containerId || currentContainerId;\r\n\t\tconst buttonId = settingAnchorEl.buttonId || currentButtonId;\r\n\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId,\r\n\t\t\tbuttonId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\thandleClose();\r\n\t};\r\n\r\n\tconst handleCloseSettingPopup = (_containerId: string, _buttonId: string) => {\r\n\t\t// updateButtonAction(containerId, buttonId, {\r\n\t\t// \tvalue: selectedActions,\r\n\t\t// \ttargetURL: targetURL,\r\n\t\t// \ttab: selectedTab,\r\n\t\t// \tinteraction: null,\r\n\t\t// });\r\n\t\t// updateButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId: \"\",\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\t// Clear the design button settings if the deleted button was previously selected\r\n\tconst deletebuttonidindesign = (buttonid: any, deletebuttonid: any) => {\r\n\t\tif (buttonid === deletebuttonid) {\r\n\t\t\tconst targetStep = { ...updatedGuideData?.GuideStep?.[currentStep - 1] };\r\n\t\t\tif (targetStep && targetStep.Design) {\r\n\t\t\t\ttargetStep.Design.GotoNext = {\r\n\t\t\t\t\tButtonId: \"\",\r\n\t\t\t\t\tButtonName: \"\",\r\n\t\t\t\t\tElementPath: \"\",\r\n\t\t\t\t\tNextStep: \"element\",\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\tupdatedGuideData.GuideStep[currentStep - 1] = targetStep;\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleApplyChanges = (\r\n\t\ttempColors: any, // Changed from string to any to handle the object structure\r\n\t\tselectedActions: string,\r\n\t\ttargetURL: string,\r\n\t\tselectedInteraction: string,\r\n\t\tcurrentButtonName: string,\r\n\t\tselectedTab: string\r\n\t) => {\r\n\t\t// Get the container and button IDs\r\n\t\tconst { containerId, buttonId } = settingAnchorEl;\r\n\t\t// Update the button style - make sure we're passing the correct structure\r\n\t\tupdateButton(containerId, buttonId, \"style\", {\r\n\t\t\tbackgroundColor: tempColors.backgroundColor,\r\n\t\t\tborderColor: tempColors.borderColor,\r\n\t\t\tcolor: tempColors.color\r\n\t\t});\r\n\t\tupdateTooltipButtonAction(containerId, buttonId, {\r\n\t\t\tvalue: selectedActions,\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab,\r\n\t\t\tinteraction: null,\r\n\t\t});\r\n\t\tupdateTooltipButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\t\tupdateButton(containerId, buttonId, \"name\", currentButtonName);\r\n\r\n\t\t// Clear selection\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Box\r\n\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\tid={buttonsContainer.id}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\theight: \"60px\",\r\n\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\tpadding: \"0px\",\r\n\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\tbackgroundColor: buttonsContainer.style.backgroundColor,\r\n\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\t// onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\r\n\t\t\t\t// onMouseLeave={(e) => setCurrentContainerId(\"\")}\r\n\t\t\t>\r\n\t\t\t\t{buttonsContainer.buttons.map((item: any) => {\r\n\t\t\t\t\treturn (\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t// flex: 1,\r\n\t\t\t\t\t\t\t\tjustifyContent: `center`,\r\n\t\t\t\t\t\t\t\t// \"&:hover .edit-icon\": { display: \"inline-flex\" },\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonMouseLeave={() => {\r\n\t\t\t\t\t\t\t\tsetIsDeleteIcon(\"\");\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t// contentEditable={item.isEditing}\r\n\t\t\t\t\t\t\t\tonMouseOver={(e) => {\r\n\t\t\t\t\t\t\t\t\tif (item.isEditing === false && e.currentTarget.id === item.id) {\r\n\t\t\t\t\t\t\t\t\t\tsetIsDeleteIcon(item.id);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tid={item.id}\r\n\t\t\t\t\t\t\t\tvariant={\"contained\"}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\ttransition: \"none\",\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\tborder: item.style.borderColor,\r\n\t\t\t\t\t\t\t\t\t//border: `${item.type !== \"primary\" ? item.style.borderColor : \"none\"}`,\r\n\t\t\t\t\t\t\t\t\tcolor: `${item.style.color}`,\r\n\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"4px 8px !important\",\r\n\t\t\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\twidth: \"fit-content\",\r\n\t\t\t\t\t\t\t\t\t//boxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor, // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={(e) => handleClick(e, buttonsContainer.id, item.id)} // Open popover when clicking the button\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(item.name, { defaultValue: item.name })}\r\n\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t{buttonsContainer.buttons.length > 1 && isDeleteIcon === item.id ? (\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"del-icon\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t\ttop: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\tright: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t//boxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t// display: \"none\", // Initially hidden\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\r\n\t\t\t\t\t\t\t\t\t\tzIndex: \"1\",\r\n\t\t\t\t\t\t\t\t\t\tpadding : \"3px !important\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", \r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tspan: {\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"14px\"\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"14px\", \r\n\t\t\t\t\t\t\t\t\t\t\theight: \"14px\", \r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"#ff0000\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\tdeletebuttonidindesign(item.id, toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId);\r\n\t\t\t\t\t\t\t\t\t\tdeleteButton(\r\n\t\t\t\t\t\t\t\t\t\t\titem.id,\r\n\t\t\t\t\t\t\t\t\t\t\tbuttonsContainer.id,\r\n\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId\r\n\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\tsetElementClick(\"element\");\r\n\t\t\t\t\t\t\t\t\t\t// setButtonClick(false);\r\n\t\t\t\t\t\t\t\t\t\tSetElementButtonClick(false);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t);\r\n\t\t\t\t})}\r\n\t\t\t\t{shouldShowAddBtn < 4 ? (\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\tpadding: \"6px !important\",\r\n\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t// sx={sideAddButtonStyle}\r\n\t\t\t\t\t\tonClick={() => handleAddIconClick(buttonsContainer.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<AddIcon\r\n\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t) : null}\r\n\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tid={\"button-toolbar\"}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\t// anchorEl={anchorEl}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\tanchorPosition={{\r\n\t\t\t\t\t\ttop: anchorEl?.getBoundingClientRect().top || 0,\r\n\t\t\t\t\t\tleft: anchorEl?.getBoundingClientRect().left || 0,\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\tpadding: \"4px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/*\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", fontWeight: \"bold\" }}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid=\"primary\"\r\n\t\t\t\t\t\t\tonClick={(e) =>\r\n\t\t\t\t\t\t\t\thandleChangeButton(settingAnchorEl.containerId, settingAnchorEl.buttonId, e.currentTarget.id)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tPrimary\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", color: \"gray\" }}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid=\"secondary\"\r\n\t\t\t\t\t\t\tonClick={(e) =>\r\n\t\t\t\t\t\t\t\thandleChangeButton(settingAnchorEl.containerId, settingAnchorEl.buttonId, e.currentTarget.id)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tSecondary\r\n\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t<Box sx={{ borderLeft: \"1px solid #ccc\", height: \"24px\", marginLeft: \"8px\" }}></Box>\r\n                       */}\r\n\t\t\t\t\t\t{/* Icons for additional options */}\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={handleSettingIconClick}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: settingsicon }}\r\n\t\t\t\t\t\t\t\tstyle={{width:\"20px\", height:\"20px\"}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\t\ttitle=\"Background Color\"\r\n\t\t\t\t\t\t\tstyle={{ zIndex: 99999 }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\tbuttonsContainer.style.backgroundColor === \"#5f9ea0\"\r\n\t\t\t\t\t\t\t\t\t\t\t? buttonsContainer.style.backgroundColor\r\n\t\t\t\t\t\t\t\t\t\t\t: \"#e0dbdb\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t//border: `1px solid red`,\r\n\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tcloneButtonContainer(settingAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\tsetAnchorEl(null);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\ttitle={isCloneDisabled ? \"Maximum limit of 3 Button sections reached\" : \"Clone Section\"}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1, width:\"20px\", height:\"20px\" }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t// disabled={buttonsContainer.buttons.length === 1}\r\n\t\t\t\t\t\t\tonClick={handleDelteContainer}\r\n\t\t\t\t\t\t\tdisabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\topacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1 ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"24px\"\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\r\n\t\t\t\t<ButtonSetting\r\n\t\t\t\t\thandleCloseSettingPopup={handleCloseSettingPopup}\r\n\t\t\t\t\tsettingAnchorEl={settingAnchorEl}\r\n\t\t\t\t\tbuttonInfo={buttonInfo}\r\n\t\t\t\t\thandleApplyChanges={handleApplyChanges}\r\n\t\t\t\t\tupdatedGuideData={updatedGuideData}\r\n\t\t\t\t/>\r\n\t\t\t</Box>\r\n\r\n\t\t\t{/* Color Picker Popover */}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\tid=\"color-picker\"\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={buttonsContainer.style.backgroundColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ButtonSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AAChD,SAASC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAyBC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAChG,SAASC,YAAY,QAAqB,aAAa;AACvD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,QAAuC,6BAA6B;AAC/G,OAAOC,cAAc,MAAoC,4BAA4B;AACrF,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,aAAqG,GAAGA,CAAC;EAC9GC,KAAK,EAAEC,gBAAgB;EACvBC,gBAAgB;EAChBC;AACD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;EACL,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGhB,cAAc,CAAC,CAAC;EACzC,MAAM;IACL;IACAiB,yBAAyB,EAAEC,eAAe;IAC1CC,2BAA2B,EAAEC,oBAAoB;IACjDC,qBAAqB,EAAEC,YAAY;IACnCC,qBAAqB,EAAEC,YAAY;IACnCC,qBAAqB,EAAEC,YAAY;IACnCC,8BAA8B;IAC9BC,yBAAyB;IACzBC,4BAA4B,EAAEC,qBAAqB;IACnDC,yBAAyB,EAAEC,eAAe;IAC1CC,4BAA4B,EAAEC,kBAAkB;IAChDC,WAAW;IACXC,UAAU;IACVC,oBAAoB;IACpBC,oBAAoB;IACpBC,iBAAiB;IACjBC,eAAe;IACfC;EACD,CAAC,GAAG5C,cAAc,CAAE6C,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzD,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAAC0D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3D,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC1D;EACA,IAAIkE,YAA4B;EAChC,MAAMC,WAAW,GAAGA,CAACC,KAAoC,EAAEC,WAAmB,EAAEC,QAAgB,KAAK;IACpG,MAAMC,MAAM,GAAGH,KAAK,CAACI,aAAa;IAClCf,WAAW,CAACc,MAAM,CAAC;IACnBxB,kBAAkB,CAAC;MAClBsB,WAAW;MACXC,QAAQ;MACRG,KAAK,EAAE;IACR,CAAC,CAAC;;IAEF;IACAV,qBAAqB,CAACM,WAAW,CAAC;IAClCJ,kBAAkB,CAACK,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACzBjB,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMkB,0BAA0B,GAAIP,KAAoC,IAAK;IAC5ET,sBAAsB,CAACS,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;EAED,MAAMI,iBAAiB,GAAIC,KAAkB,IAAK;IACjD;IACAhC,eAAe,CAACd,eAAe,CAACsC,WAAW,EAAE,OAAO,EAAE;MACrDS,eAAe,EAAED,KAAK,CAACE;IACxB,CAAC,CAAC;;IAEF;IACAlC,eAAe,CAACd,eAAe,CAACsC,WAAW,EAAE,iBAAiB,EAAEQ,KAAK,CAACE,GAAG,CAAC;EAC3E,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACpCrB,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMsB,IAAI,GAAGC,OAAO,CAAC1B,QAAQ,CAAC;EAC9B;EACA;EACA,MAAM2B,eAAe,GAAGD,OAAO,CAACxB,mBAAmB,CAAC;EAEpD,MAAM0B,oBAAoB,GAAGA,CAC5Bf,WAAmB,EACnBC,QAAgB,EAChBe,SAAwB,EACxBZ,KAA6B,KACzB;IACJ;IACAtC,YAAY,CAACkC,WAAW,EAAEC,QAAQ,EAAEe,SAAS,EAAEZ,KAAK,CAAC;EACtD,CAAC;EAED,MAAMa,kBAAkB,GAAGA,CAACjB,WAAmB,EAAEC,QAAgB,EAAEG,KAA6B,KAAK;IACpGtC,YAAY,CAACkC,WAAW,EAAEC,QAAQ,EAAE,MAAM,EAAEG,KAAK,CAAC;IAClDhB,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAM8B,kBAAkB,GAAIlB,WAAmB,IAAK;IACnDhC,YAAY,CAACgC,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMmB,gBAAgB,GAAGpE,gBAAgB,CAACqE,OAAO,CAACC,MAAM;EAExD,MAAMC,UAAU,GAAG5F,OAAO,CAAC,MAAM;IAChC,IAAI6F,MAAM,GAAG,IAAI;IACjB,IAAI7D,eAAe,CAACuC,QAAQ,EAAE;MAC7BsB,MAAM,GAAGxE,gBAAgB,CAACqE,OAAO,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,EAAE,KAAKhE,eAAe,CAACuC,QAAQ,CAAC;IAC5F;IACA,OAAOsB,MAAM;EACd,CAAC,EAAE,CAAC7D,eAAe,CAACuC,QAAQ,EAAElD,gBAAgB,CAACqE,OAAO,CAAC,CAAC;;EAExD;;EAEA;EACA;EACA,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IAClCrD,qBAAqB,CAACZ,eAAe,CAACsC,WAAW,CAAC;IAClDhB,eAAe,CAAC,SAAS,CAAC;IAC1B,MAAM4C,WAAW,GAAG;MAAE,GAAG5E;IAAiB,CAAC;IAC3C,IAAI4E,WAAW,CAACC,SAAS,IAAID,WAAW,CAACC,SAAS,CAAClD,WAAW,CAAC,EAAE;MAChE,MAAMmD,QAAQ,GAAG;QAAE,GAAGF,WAAW,CAACC,SAAS,CAAClD,WAAW,GAAG,CAAC;MAAE,CAAC;;MAE9D;MACA,IAAImD,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACC,MAAM,CAACC,QAAQ,IAAIF,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKC,SAAS,EAAE;QACnGJ,QAAQ,CAACC,MAAM,GAAG;UACjB,GAAGD,QAAQ,CAACC,MAAM;UAClBC,QAAQ,EAAE;YACT,GAAGF,QAAQ,CAACC,MAAM,CAACC,QAAQ;YAC3BC,QAAQ,EAAE,EAAE;YACZE,QAAQ,EAAE;UACX;QACD,CAAC;MACF;MACAvD,UAAU,CAAC,EAAE,CAAC;MACd;MACAgD,WAAW,CAACC,SAAS,GAAG,CAAC,GAAGD,WAAW,CAACC,SAAS,CAAC;MAClDD,WAAW,CAACC,SAAS,CAAClD,WAAW,GAAG,CAAC,CAAC,GAAGmD,QAAQ;MACjD9E,gBAAgB,GAAG4E,WAAW;IAC/B;IAEAhD,UAAU,CAAC,EAAE,CAAC;IACdQ,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMgD,sBAAsB,GAAIrC,KAAoC,IAAK;IACxE;IACA,MAAMC,WAAW,GAAGtC,eAAe,CAACsC,WAAW,IAAIP,kBAAkB;IACrE,MAAMQ,QAAQ,GAAGvC,eAAe,CAACuC,QAAQ,IAAIN,eAAe;IAE5DjB,kBAAkB,CAAC;MAClBsB,WAAW;MACXC,QAAQ;MACR;MACAG,KAAK,EAAEL,KAAK,CAACI;IACd,CAAC,CAAC;IACFE,WAAW,CAAC,CAAC;EACd,CAAC;EAED,MAAMgC,uBAAuB,GAAGA,CAACC,YAAoB,EAAEC,SAAiB,KAAK;IAC5E;IACA;IACA;IACA;IACA;IACA;IACA;IACA7D,kBAAkB,CAAC;MAClBsB,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZG,KAAK,EAAE;IACR,CAAC,CAAC;IACFhB,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;;EAED;EACA,MAAMoD,sBAAsB,GAAGA,CAACC,QAAa,EAAEC,cAAmB,KAAK;IACtE,IAAID,QAAQ,KAAKC,cAAc,EAAE;MAAA,IAAAC,iBAAA,EAAAC,qBAAA;MAChC,MAAMC,UAAU,GAAG;QAAE,KAAAF,iBAAA,GAAG3F,gBAAgB,cAAA2F,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBd,SAAS,cAAAe,qBAAA,uBAA3BA,qBAAA,CAA8BjE,WAAW,GAAG,CAAC,CAAC;MAAC,CAAC;MACxE,IAAIkE,UAAU,IAAIA,UAAU,CAACd,MAAM,EAAE;QACpCc,UAAU,CAACd,MAAM,CAACC,QAAQ,GAAG;UAC5BC,QAAQ,EAAE,EAAE;UACZa,UAAU,EAAE,EAAE;UACdC,WAAW,EAAE,EAAE;UACfZ,QAAQ,EAAE;QACX,CAAC;MACF;MACAnF,gBAAgB,CAAC6E,SAAS,CAAClD,WAAW,GAAG,CAAC,CAAC,GAAGkE,UAAU;IACzD;EACD,CAAC;EAGD,MAAMG,kBAAkB,GAAGA,CAC1BC,UAAe,EACfC,eAAuB,EACvBC,SAAiB,EACjBC,mBAA2B,EAC3BC,iBAAyB,EACzBC,WAAmB,KACf;IACJ;IACA,MAAM;MAAEtD,WAAW;MAAEC;IAAS,CAAC,GAAGvC,eAAe;IACjD;IACAI,YAAY,CAACkC,WAAW,EAAEC,QAAQ,EAAE,OAAO,EAAE;MAC5CQ,eAAe,EAAEwC,UAAU,CAACxC,eAAe;MAC3C8C,WAAW,EAAEN,UAAU,CAACM,WAAW;MACnC/C,KAAK,EAAEyC,UAAU,CAACzC;IACnB,CAAC,CAAC;IACFpC,yBAAyB,CAAC4B,WAAW,EAAEC,QAAQ,EAAE;MAChDG,KAAK,EAAE8C,eAAe;MACtBC,SAAS,EAAEA,SAAS;MACpBK,GAAG,EAAEF,WAAW;MAChBG,WAAW,EAAE;IACd,CAAC,CAAC;IACFtF,8BAA8B,CAAC6B,WAAW,EAAEC,QAAQ,EAAEmD,mBAAmB,CAAC;IAC1EtF,YAAY,CAACkC,WAAW,EAAEC,QAAQ,EAAE,MAAM,EAAEoD,iBAAiB,CAAC;;IAE9D;IACA3E,kBAAkB,CAAC;MAAEsB,WAAW,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEG,KAAK,EAAE;IAAK,CAAC,CAAC;IAClEhB,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,oBACC1C,OAAA,CAAAE,SAAA;IAAA8G,QAAA,gBACChH,OAAA,CAACd,GAAG;MACH+H,SAAS,EAAE,KAAM;MACjBjC,EAAE,EAAE3E,gBAAgB,CAAC2E,EAAG;MACxBkC,EAAE,EAAE;QACHC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,KAAK;QACVC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,YAAY;QACvB1D,eAAe,EAAE1D,gBAAgB,CAACqH,KAAK,CAAC3D,eAAe;QACvD4D,cAAc,EAAE;MACjB;MACA;MACA;MAAA;MAAAX,QAAA,GAEC3G,gBAAgB,CAACqE,OAAO,CAACkD,GAAG,CAAE7C,IAAS,IAAK;QAC5C,oBACC/E,OAAA,CAACd,GAAG;UACHgI,EAAE,EAAE;YACHW,QAAQ,EAAE,UAAU;YACpBR,OAAO,EAAE,MAAM;YACf;YACAM,cAAc,EAAE;YAChB;UACD,CAAE;UACFG,YAAY,EAAEA,CAAA,KAAM;YACnBhF,eAAe,CAAC,EAAE,CAAC;UACpB,CAAE;UAAAkE,QAAA,gBAEFhH,OAAA,CAACb;UACA;UAAA;YACA4I,WAAW,EAAGC,CAAC,IAAK;cACnB,IAAIjD,IAAI,CAACT,SAAS,KAAK,KAAK,IAAI0D,CAAC,CAACvE,aAAa,CAACuB,EAAE,KAAKD,IAAI,CAACC,EAAE,EAAE;gBAC/DlC,eAAe,CAACiC,IAAI,CAACC,EAAE,CAAC;cACzB;YACD,CAAE;YACFA,EAAE,EAAED,IAAI,CAACC,EAAG;YACZiD,OAAO,EAAE,WAAY;YACrBf,EAAE,EAAE;cACHgB,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE,MAAM;cAClBC,SAAS,EAAE,iBAAiB;cAC5BC,MAAM,EAAEtD,IAAI,CAAC2C,KAAK,CAACb,WAAW;cAC9B;cACA/C,KAAK,EAAE,GAAGiB,IAAI,CAAC2C,KAAK,CAAC5D,KAAK,EAAE;cAC5BwE,aAAa,EAAE,MAAM;cACrBd,OAAO,EAAE,oBAAoB;cAC7Be,UAAU,EAAE,0BAA0B;cACtCC,QAAQ,EAAE,iBAAiB;cAC3BzE,eAAe,EAAEgB,IAAI,CAAC2C,KAAK,CAAC3D,eAAe;cAC3CqD,KAAK,EAAE,aAAa;cACpB;cACA,SAAS,EAAE;gBACVrD,eAAe,EAAEgB,IAAI,CAAC2C,KAAK,CAAC3D,eAAe;gBAAE;gBAC7C0E,OAAO,EAAE,GAAG;gBAAE;gBACdL,SAAS,EAAE,iBAAiB,CAAE;cAC/B;YACD,CAAE;YACFM,OAAO,EAAGV,CAAC,IAAK5E,WAAW,CAAC4E,CAAC,EAAE3H,gBAAgB,CAAC2E,EAAE,EAAED,IAAI,CAACC,EAAE,CAAE,CAAC;YAAA;YAAAgC,QAAA,EAE7DlG,SAAS,CAACiE,IAAI,CAAC4D,IAAI,EAAE;cAAEC,YAAY,EAAE7D,IAAI,CAAC4D;YAAK,CAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,EAER3I,gBAAgB,CAACqE,OAAO,CAACC,MAAM,GAAG,CAAC,IAAI9B,YAAY,KAAKkC,IAAI,CAACC,EAAE,gBAC/DhF,OAAA,CAACX,UAAU;YACV4J,IAAI,EAAC,OAAO;YACZC,SAAS,EAAC,UAAU;YACpBhC,EAAE,EAAE;cACHW,QAAQ,EAAE,UAAU;cACpBsB,GAAG,EAAE,OAAO;cACZC,KAAK,EAAE,OAAO;cACdrF,eAAe,EAAE,MAAM;cACvB;cACA;cACAqE,SAAS,EAAE,gCAAgC;cAC3CiB,MAAM,EAAE,GAAG;cACX7B,OAAO,EAAG,gBAAgB;cAC1B,SAAS,EAAE;gBACVzD,eAAe,EAAE,MAAM;gBACvBqE,SAAS,EAAE;cACZ,CAAC;cACDkB,IAAI,EAAE;gBACLnC,MAAM,EAAE;cACT,CAAC;cACDoC,GAAG,EAAE;gBACJnC,KAAK,EAAE,MAAM;gBACbD,MAAM,EAAE,MAAM;gBACdqC,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YACFf,OAAO,EAAGV,CAAC,IAAK;cAAA,IAAA0B,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cACf/B,CAAC,CAACgC,eAAe,CAAC,CAAC;cACnBlE,sBAAsB,CAACf,IAAI,CAACC,EAAE,GAAA0E,qBAAA,GAAEtH,oBAAoB,CAACH,WAAW,GAAG,CAAC,CAAC,cAAAyH,qBAAA,wBAAAC,sBAAA,GAArCD,qBAAA,CAAuCO,MAAM,cAAAN,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CO,QAAQ,cAAAN,sBAAA,uBAAvDA,sBAAA,CAAyDrE,QAAQ,CAAC;cAClG/D,YAAY,CACXuD,IAAI,CAACC,EAAE,EACP3E,gBAAgB,CAAC2E,EAAE,GAAA6E,sBAAA,GACnBzH,oBAAoB,CAACH,WAAW,GAAG,CAAC,CAAC,cAAA4H,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCI,MAAM,cAAAH,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CI,QAAQ,cAAAH,sBAAA,uBAAvDA,sBAAA,CAAyDxE,QAC1D,CAAC;cACDjD,eAAe,CAAC,SAAS,CAAC;cAC1B;cACAC,qBAAqB,CAAC,KAAK,CAAC;YAC7B,CAAE;YAAAyE,QAAA,eAEFhH,OAAA;cAAMmK,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5K;cAAW;YAAE;cAAAqJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,GACV,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAER,CAAC,CAAC,EACDvE,gBAAgB,GAAG,CAAC,gBACpBzE,OAAA,CAACX,UAAU;QACV6H,EAAE,EAAE;UACHnD,eAAe,EAAE,SAAS;UAC1BsG,MAAM,EAAE,SAAS;UACjBhB,MAAM,EAAE,IAAI;UACZ7B,OAAO,EAAE,gBAAgB;UACzBY,SAAS,EAAE,iBAAiB;UAAE;UAC9B,SAAS,EAAE;YACVrE,eAAe,EAAE,SAAS;YAC1BqE,SAAS,EAAE,iBAAiB,CAAE;UAC/B;QACD;QACA;QAAA;QACAM,OAAO,EAAEA,CAAA,KAAMlE,kBAAkB,CAACnE,gBAAgB,CAAC2E,EAAE,CAAE;QAAAgC,QAAA,eAEvDhH,OAAA,CAACJ,OAAO;UACP4I,QAAQ,EAAC,OAAO;UAChBtB,EAAE,EAAE;YAAEpD,KAAK,EAAE;UAAO;QAAE;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,GACV,IAAI,eAERhJ,OAAA,CAACZ,OAAO;QACP4F,EAAE,EAAE,gBAAiB;QACrBd,IAAI,EAAEA;QACN;QAAA;QACAoG,OAAO,EAAE3G,WAAY;QACrB4G,eAAe,EAAC,gBAAgB;QAChCC,cAAc,EAAE;UACfrB,GAAG,EAAE,CAAA1G,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgI,qBAAqB,CAAC,CAAC,CAACtB,GAAG,KAAI,CAAC;UAC/CuB,IAAI,EAAE,CAAAjI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgI,qBAAqB,CAAC,CAAC,CAACC,IAAI,KAAI;QACjD,CAAE;QACFC,YAAY,EAAE;UACbC,QAAQ,EAAE,KAAK;UACfC,UAAU,EAAE;QACb,CAAE;QACFC,eAAe,EAAE;UAChBF,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE;QACb,CAAE;QACFE,SAAS,EAAE;UACVC,IAAI,EAAE;YACL;YACA9D,EAAE,EAAE;cACHmC,MAAM,EAAG4B,KAAK,IAAKA,KAAK,CAAC5B,MAAM,CAAC6B,OAAO,GAAG;YAC3C;UACD;QACD,CAAE;QAAAlE,QAAA,eAEFhH,OAAA,CAACd,GAAG;UACHgI,EAAE,EAAE;YACHG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,KAAK;YACVC,OAAO,EAAE;UACV,CAAE;UAAAR,QAAA,gBA4BFhH,OAAA,CAACX,UAAU;YACV4J,IAAI,EAAC,OAAO;YACZP,OAAO,EAAEhD,sBAAuB;YAChCwB,EAAE,EAAE;cACHkB,SAAS,EAAE,iBAAiB;cAAE;cAC9B,SAAS,EAAE;gBACVA,SAAS,EAAE,iBAAiB,CAAE;cAC/B;YACD,CAAE;YAAApB,QAAA,eAEFhH,OAAA;cAAMmK,uBAAuB,EAAE;gBAAEC,MAAM,EAAE1K;cAAa,CAAE;cACvDgI,KAAK,EAAE;gBAACN,KAAK,EAAC,MAAM;gBAAED,MAAM,EAAC;cAAM;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACbhJ,OAAA,CAACV,OAAO;YAAC6L,KAAK;YACbC,KAAK,EAAC,kBAAkB;YACxB1D,KAAK,EAAE;cAAE2B,MAAM,EAAE;YAAM,CAAE;YAAArC,QAAA,eAEzBhH,OAAA,CAACd,GAAG;cACHgI,EAAE,EAAE;gBACHnD,eAAe,EACd1D,gBAAgB,CAACqH,KAAK,CAAC3D,eAAe,KAAK,SAAS,GACjD1D,gBAAgB,CAACqH,KAAK,CAAC3D,eAAe,GACtC,SAAS;gBACbqD,KAAK,EAAE,MAAM;gBACbD,MAAM,EAAE,MAAM;gBACde,YAAY,EAAE,KAAK;gBACnB;gBACAmD,SAAS,EAAE;cACZ,CAAE;cACFpE,SAAS,EAAE,KAAM;cACjBqE,IAAI,EAAC,QAAQ;cACb5C,OAAO,EAAE9E;YAA2B;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACVhJ,OAAA,CAACX,UAAU;YACV4J,IAAI,EAAC,OAAO;YACZP,OAAO,EAAEA,CAAA,KAAM;cACdxH,oBAAoB,CAACF,eAAe,CAACsC,WAAW,CAAC;cACjDZ,WAAW,CAAC,IAAI,CAAC;YAClB,CAAE;YACF6I,QAAQ,EAAEhL,eAAgB;YAC1B6K,KAAK,EAAE7K,eAAe,GAAG,4CAA4C,GAAG,eAAgB;YACxF2G,EAAE,EAAE;cACHkB,SAAS,EAAE,iBAAiB;cAAE;cAC9B,SAAS,EAAE;gBACVA,SAAS,EAAE,iBAAiB,CAAE;cAC/B;YACD,CAAE;YAAApB,QAAA,eAEFhH,OAAA;cACCmK,uBAAuB,EAAE;gBAAEC,MAAM,EAAE3K;cAAS,CAAE;cAC9CiI,KAAK,EAAE;gBAAEe,OAAO,EAAElI,eAAe,GAAG,GAAG,GAAG,CAAC;gBAAE6G,KAAK,EAAC,MAAM;gBAAED,MAAM,EAAC;cAAO;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACbhJ,OAAA,CAACX,UAAU;YACV4J,IAAI,EAAC;YACL;YAAA;YACAP,OAAO,EAAEzD,oBAAqB;YAC9BsG,QAAQ,EAAE,EAAA9K,sBAAA,GAAA2B,oBAAoB,CAACH,WAAW,GAAG,CAAC,CAAC,cAAAxB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuC+K,UAAU,cAAA9K,sBAAA,uBAAjDA,sBAAA,CAAmDiE,MAAM,MAAK,CAAE;YAC1EuC,EAAE,EAAE;cACHkB,SAAS,EAAE,iBAAiB;cAAE;cAC9B,SAAS,EAAE;gBACVA,SAAS,EAAE,iBAAiB,CAAE;cAC/B;YACD,CAAE;YAAApB,QAAA,eAEFhH,OAAA;cACCmK,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5K;cAAW,CAAE;cAChDkI,KAAK,EAAE;gBACNe,OAAO,EAAE,EAAA9H,sBAAA,GAAAyB,oBAAoB,CAACH,WAAW,GAAG,CAAC,CAAC,cAAAtB,sBAAA,wBAAAC,uBAAA,GAArCD,sBAAA,CAAuC6K,UAAU,cAAA5K,uBAAA,uBAAjDA,uBAAA,CAAmD+D,MAAM,MAAK,CAAC,GAAG,GAAG,GAAG,CAAC;gBAClF8G,aAAa,EAAE,MAAM;gBACrBrE,KAAK,EAAE,MAAM;gBACbD,MAAM,EAAE;cACT;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEVhJ,OAAA,CAACH,aAAa;QACb8F,uBAAuB,EAAEA,uBAAwB;QACjD3E,eAAe,EAAEA,eAAgB;QACjC4D,UAAU,EAAEA,UAAW;QACvB0B,kBAAkB,EAAEA,kBAAmB;QACvChG,gBAAgB,EAAEA;MAAiB;QAAAuI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhJ,OAAA,CAACZ,OAAO;MACP8E,IAAI,EAAEE,eAAgB;MACtB3B,QAAQ,EAAEE,mBAAoB;MAC9B2H,OAAO,EAAErG,sBAAuB;MAChC0G,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFC,eAAe,EAAE;QAChBF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACF7F,EAAE,EAAC,cAAc;MACjB+F,SAAS,EAAE;QACVC,IAAI,EAAE;UACL;UACA9D,EAAE,EAAE;YACHmC,MAAM,EAAG4B,KAAK,IAAKA,KAAK,CAAC5B,MAAM,CAAC6B,OAAO,GAAG;UAC3C;QACD;MACD,CAAE;MAAAlE,QAAA,eAEFhH,OAAA,CAACd,GAAG;QAAA8H,QAAA,gBACHhH,OAAA,CAACT,YAAY;UACZuE,KAAK,EAAEzD,gBAAgB,CAACqH,KAAK,CAAC3D,eAAgB;UAC9C2H,QAAQ,EAAE7H;QAAkB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFhJ,OAAA;UAAAgH,QAAA,EACE;AACP;AACA;AACA;AACA;QAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACT,CAAC;AAEL,CAAC;AAACxI,EAAA,CA5hBIL,aAAqG;EAAA,QAKjFL,cAAc,EAoBnCH,cAAc;AAAA;AAAAgM,EAAA,GAzBbxL,aAAqG;AA8hB3G,eAAeA,aAAa;AAAC,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}