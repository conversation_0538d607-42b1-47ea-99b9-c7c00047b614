// Initialize openTabs from storage when background script loads
let openTabs = {};
let scrapingTabs = {}; // Track tabs where scraping is active

// Load the open tabs state from storage
chrome.storage.local.get('openTabsState', (result) => {
  if (result.openTabsState) {
    openTabs = result.openTabsState;
  }
});

// Function to save openTabs state to storage
function saveOpenTabsState() {
  chrome.storage.local.set({ 'openTabsState': openTabs });
}
// Listen for page loads/reloads
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // Only run when the page has completely loaded
  if (changeInfo.status === 'complete' && tab.url) {
    // --- BEGIN: Auto-launch builder from URL parameter ---
    try {
      const url = new URL(tab.url);
      const guideId = url.searchParams.get('quickadopt_guide_id');

      if (guideId) {
        // If the guideId parameter is present, mark this tab as open and inject scripts
        openTabs[tab.id.toString()] = true;
        saveOpenTabsState();
        injectScripts(tab.id);
        // Do not process other rules for this update
        return;
      }
    } catch (e) {
      // Ignore URL parsing errors for invalid URLs
    }
    // --- END: Auto-launch builder from URL parameter ---

    // Check if URL isn't in restricted sites
    const restrictedSites = [
      "user.quickadopt.in",
      "web.quickadopt.in",
      "devuser.quickadopt.in",
      "devweb.quickadopt.in",
      "qauser.quickadopt.in",
      "homeids.quixy.com",
      "app.quixy.com",
      "app.kwixee.co.in",
      "app.kwixeesystem.in",
      "app.kwixeesit.com",
      "qaweb.quickadopt.in",
      "automationuser.quickadopt.in",
      "automationweb.quickadopt.in",
      "google.com"
    ];
    const url = new URL(tab.url);
    const hostname = url.hostname;

    // Convert tabId to string for storage compatibility
    const tabIdStr = tabId.toString();

    // Only inject if this tab was previously opened and not on restricted sites
    if (openTabs[tabIdStr] && !restrictedSites.some(site => hostname.includes(site))) {
      injectScripts(tabId);
    }
  }
});

// Handler for icon click
chrome.action.onClicked.addListener((tab) => {
  // Mark this tab as having the extension open (use string for storage compatibility)
  openTabs[tab.id.toString()] = true;
  saveOpenTabsState();
  injectScripts(tab.id);
});

// Function to clear all authentication data
function clearAllAuthData() {
  return new Promise((resolve) => {
    // Clear data from chrome.storage.local
    chrome.storage.local.remove(['user-info-storage', 'authToken'], () => {
      // Try to clear localStorage in all tabs
      chrome.tabs.query({}, (tabs) => {
        let completedTabs = 0;
        const totalTabs = tabs.length;

        if (totalTabs === 0) {
          resolve();
          return;
        }

        tabs.forEach(tab => {
          try {
            chrome.scripting.executeScript({
              target: { tabId: tab.id },
              func: () => {
                // Clear all authentication-related data from localStorage
                localStorage.removeItem('user-info-storage');
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                localStorage.removeItem('userType');
              }
            }, () => {
              completedTabs++;
              if (completedTabs === totalTabs) {
                resolve();
              }
            });
          } catch (e) {
            // Ignore errors for tabs where we can't inject scripts
            completedTabs++;
            if (completedTabs === totalTabs) {
              resolve();
            }
          }
        });
      });
    });
  });
}

chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: "quickadapt-contextmenu",
    title: "Open QuickAdopt",
    contexts: ["page"]
  });
  chrome.runtime.setUninstallURL('https://devweb.quickadopt.in/uninstall');
  chrome.storage.local.set({ 'isReinstalled': true });
  clearAllAuthData().then(() => {
  });
});


chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "quickadapt-contextmenu") {
    // Mark this tab as having the extension open
    openTabs[tab.id.toString()] = true;
    saveOpenTabsState();
    injectScripts(tab.id);
  }
});

// Add a listener for tab removal to clean up our tracking
chrome.tabs.onRemoved.addListener((tabId) => {
  const tabIdStr = tabId.toString();
  if (openTabs[tabIdStr]) {
    delete openTabs[tabIdStr];
    saveOpenTabsState();
  }
});

function injectScripts(tabId) {
  chrome.scripting.executeScript({
    target: { tabId: tabId },
    files: ['dist/content.bundle.js']
  }, () => {
    chrome.scripting.insertCSS({
      target: { tabId: tabId },
      files: ['static/css/main.a40b8379.css']
    });
  });
}

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "saveState") {
    // Save state to chrome.storage.local with the URL as the key
    chrome.storage.local.set({
      [sender.tab.url]: message.state
    }, () => {
      sendResponse({ success: true });
    });
    return true; // Required for async sendResponse
  }

  if (message.action === "startScraping" && sender.tab) {
    const tabIdStr = sender.tab.id.toString();

    // Mark this tab as scraping (continuous by default)
    scrapingTabs[tabIdStr] = {
      active: true,
      continuous: true,
      url: sender.tab.url,
      maxDepth: message.maxDepth || 3,
      interval: 5000 // Always use 5 seconds interval
    };
    chrome.scripting.executeScript({
      target: { tabId: sender.tab.id },
      files: ['content-scripts/scraper.js']
    }, () => {
      if (chrome.runtime.lastError) {
        console.error("Error injecting scraper script:", chrome.runtime.lastError);
        return;
      }

      // Wait briefly before sending message to ensure script loads
      setTimeout(() => {
        chrome.tabs.sendMessage(sender.tab.id, {
          action: 'startScraping',
          maxDepth: message.maxDepth || 3
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.warn("Content script might not be ready yet:", chrome.runtime.lastError.message);
          }
        });

        // Show a small notification that scraping is active
        chrome.scripting.executeScript({
          target: { tabId: sender.tab.id },
          function: () => {
            // if (!document.getElementById('quickadapt-scraping-notification')) {
            //   const notification = document.createElement('div');
            //   notification.id = 'quickadapt-scraping-notification';
            //   notification.style.cssText = `
            //     position: fixed;
            //     top: 10px;
            //     right: 10px;
            //     background-color: rgba(95, 158, 160, 0.9);
            //     color: white;
            //     padding: 8px 12px;
            //     border-radius: 4px;
            //     z-index: 10000;
            //     font-family: Arial, sans-serif;
            //     font-size: 14px;
            //     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            //   `;
            //   notification.textContent = 'Scraping in progress (updates every 5s)...';
            //   document.body.appendChild(notification);
            // }
          }
        });
      }, 300); // Delay helps ensure scraper script has registered listener
    });


    sendResponse({ success: true, message: "Scraping started" });
    return true;
  }

  if ((message.action === "stopScraping" || message.action === "stopContinuousScraping") && sender.tab) {
    const tabIdStr = sender.tab.id.toString();
    const tabUrl = sender.tab.url;

    // Remove scraping status
    delete scrapingTabs[tabIdStr];

    // Send message to content script to stop scraping
    chrome.tabs.sendMessage(sender.tab.id, {
      action: 'stopScraping'
    });

    // Remove the notification
    chrome.scripting.executeScript({
      target: { tabId: sender.tab.id },
      function: () => {
        const notification = document.getElementById('quickadapt-scraping-notification');
        if (notification) {
          notification.remove();
        }
      }
    });

    // Log that scraping has been stopped
    console.log("Scraping stopped for tab:", tabIdStr, "URL:", tabUrl);

    sendResponse({ success: true, message: "Scraping stopped" });
    return true;
  }

  if (message.action === "scrapingComplete" && sender.tab) {
    const tabUrl = sender.tab.url;
    const storageKey = 'scrapedData_' + tabUrl;

    console.log("Received scraped data:", message.data);
    console.log("Append:", message.append, "Continuous:", message.continuous);

    // Get existing data if we're appending
    if (message.append) {
      chrome.storage.local.get([storageKey], (result) => {
        let existingData = result[storageKey];

        // If no existing data, initialize it
        if (!existingData) {
          existingData = {
            url: tabUrl,
            title: message.data.title || document.title,
            timestamp: new Date().toISOString(),
            elements: [],
            scrapes: []
          };
        }

        // Add timestamp to the new data
        const newData = {
          ...message.data,
          timestamp: new Date().toISOString()
        };

        // If this is continuous scraping, store each scrape separately
        if (message.continuous) {
          // Initialize the array of scrapes if it doesn't exist
          if (!existingData.scrapes) {
            existingData.scrapes = [];
          }

          // Add the new scrape
          existingData.scrapes.push(newData);

          // For elements that are new or changed, update them in the main elements array
          if (newData.elements && Array.isArray(newData.elements)) {
            // Create a map of existing elements by xpath for quick lookup
            const existingElementsMap = {};
            if (existingData.elements && Array.isArray(existingData.elements)) {
              existingData.elements.forEach(el => {
                if (el.xpath) {
                  existingElementsMap[el.xpath] = el;
                }
              });
            } else {
              existingData.elements = [];
            }

            // Process each new element
            newData.elements.forEach(newElement => {
              if (newElement.xpath) {
                // If element already exists, update it
                if (existingElementsMap[newElement.xpath]) {
                  Object.assign(existingElementsMap[newElement.xpath], newElement);
                } else {
                  // Otherwise add it to the elements array
                  existingData.elements.push(newElement);
                }
              } else {
                // No xpath, just add as a new element
                existingData.elements.push(newElement);
              }
            });
          }

          // Update timestamp
          existingData.timestamp = newData.timestamp;
        } else {
          // For one-time scraping, just replace the elements
          existingData.elements = newData.elements || [];
          existingData.timestamp = newData.timestamp;
        }

        console.log("Storing updated data:", existingData);

        // Store the updated data
        chrome.storage.local.set({
          [storageKey]: existingData
        }, () => {
          if (chrome.runtime.lastError) {
            console.error("Error storing scraped data:", chrome.runtime.lastError);
          } else {
            console.log("Successfully stored scraped data");
          }
        });
      });
    } else {
      // Store the scraped data (initial scrape)
      const initialData = {
        ...message.data,
        timestamp: new Date().toISOString(),
        scrapes: message.continuous ? [message.data] : []
      };

      console.log("Storing initial data:", initialData);

      chrome.storage.local.set({
        [storageKey]: initialData
      }, () => {
        if (chrome.runtime.lastError) {
          console.error("Error storing initial scraped data:", chrome.runtime.lastError);
        } else {
          console.log("Successfully stored initial scraped data");
        }
      });
    }

    // Update the notification if continuous scraping
    if (message.continuous) {
      chrome.scripting.executeScript({
        target: { tabId: sender.tab.id },
        function: () => {
          const notification = document.getElementById('quickadapt-scraping-notification');
          if (notification) {
            notification.textContent = 'Continuous scraping active... Last update: ' + new Date().toLocaleTimeString();
          }
        }
      });
    }

    sendResponse({ success: true });
    return true;
  }

  if (message.action === "getScrapedData") {
    const url = sender.tab ? sender.tab.url : message.url;
    if (url) {
      const storageKey = 'scrapedData_' + url;
      console.log("Getting scraped data for:", url);

      chrome.storage.local.get([storageKey], (result) => {
        const data = result[storageKey] || null;
        console.log("Retrieved scraped data:", data ? "Found" : "Not found");
        if (data) {
          console.log("Elements count:", data.elements ? data.elements.length : 0);
          console.log("Scrapes count:", data.scrapes ? data.scrapes.length : 0);
        }
        sendResponse({ data: data });
      });
      return true;
    }
    console.log("No URL provided for getScrapedData");
    sendResponse({ data: null });
    return true;
  }

  if (message.action === "isScrapingActive" && sender.tab) {
    const tabIdStr = sender.tab.id.toString();
    sendResponse({ isActive: !!scrapingTabs[tabIdStr] });
    return true;
  }

  if (message.action === "closeExtension" && sender.tab) {
    const tabIdStr = sender.tab.id.toString();

    // Remove the extension UI by executing content script removal function
    chrome.scripting.executeScript({
      target: { tabId: sender.tab.id },
      func: () => {
        const container = document.getElementById('my-react-drawer');
        if (container) container.remove();
      }
    });

    // Remove tab tracking
    delete openTabs[tabIdStr];
    saveOpenTabsState();

    sendResponse({ success: true });
  }

  if (message.action === "getState") {
    // Retrieve state based on URL
    chrome.storage.local.get(sender.tab.url, (result) => {
      sendResponse({ state: result[sender.tab.url] || null });
    });
    return true; // Required for async sendResponse
  }

  if (message.action === "checkTabStatus") {
    // Check if this tab should have the extension open
    const tabIdStr = sender.tab.id.toString();
    sendResponse({ shouldOpen: openTabs[tabIdStr] || false });
    return true;
  }
  if (message.action === "listAllScrapedData") {
    // List all scraped data in storage (for debugging)
    chrome.storage.local.get(null, (items) => {
      const scrapedDataKeys = Object.keys(items).filter(key => key.startsWith('scrapedData_'));
      const scrapedDataSummary = {};

      scrapedDataKeys.forEach(key => {
        const data = items[key];
        scrapedDataSummary[key] = {
          url: data.url,
          timestamp: data.timestamp,
          elementsCount: data.elements ? data.elements.length : 0,
          scrapesCount: data.scrapes ? data.scrapes.length : 0
        };
      });

      console.log("All scraped data:", scrapedDataSummary);
      sendResponse({ data: scrapedDataSummary });
    });
    return true;
  }
  if (message.action === "clearAuthData") {
    // Clear authentication data on request (e.g., logout)
    clearAllAuthData().then(() => {
      sendResponse({ success: true });
    });
    return true; // Required for async sendResponse
  }

  if (message.action === "checkReinstallStatus") {
    // Check if this is a fresh reinstall
    chrome.storage.local.get('isReinstalled', (result) => {
      const isReinstalled = result.isReinstalled || false;

      // If it is a reinstall, clear the flag so we only force login once
      if (isReinstalled) {
        chrome.storage.local.remove('isReinstalled');
      }

      sendResponse({ isReinstalled: isReinstalled });
    });
    return true; // Required for async sendResponse
  }

});


chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === "GET_SCRAPED_DATA") {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      const currentTab = tabs?.[0];
      if (!currentTab?.url) {
        sendResponse({ scrapedData: null });
        return;
      }

      const tabUrl = currentTab.url;
      const storageKey = 'scrapedData_' + tabUrl;

      chrome.storage.local.get([storageKey], (result) => {
        const scrapedData = result[storageKey];
        sendResponse({ tabUrl, scrapedData });
      });
    });

    // Important: keep the message channel open
    return true;
  }
});
