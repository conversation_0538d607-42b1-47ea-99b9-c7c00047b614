import React from 'react';
import { IconButton, Tooltip, Box } from '@mui/material';
import UndoIcon from '@mui/icons-material/Undo';
import RedoIcon from '@mui/icons-material/Redo';
import { useTranslation } from 'react-i18next';
import useDrawerStore from '../../store/drawerStore';
import { redoicon, undoicon } from '../../assets/icons/icons';

interface UndoRedoButtonsProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  disabled?: boolean;
  className?: string;
}

/**
 * UndoRedoButtons component provides undo and redo functionality
 * for the guide editor.
 */
const UndoRedoButtons: React.FC<UndoRedoButtonsProps> = ({
  size = 'medium',
  color = 'primary',
  disabled = false,
  className,
}) => {
  const { t: translate } = useTranslation();
  // Get the undo/redo functions and state from the drawer store
  const { undo, redo, canUndo, canRedo } = useDrawerStore();

  // Check if undo/redo are available
  const canUndoValue = canUndo();
  const canRedoValue = canRedo();

  return (
    <Box className="undo-redobtn" sx={{ display: 'flex', alignItems: 'center' }}>
      <Box>
        <Tooltip arrow title={translate("coming soon")}>
          <span>
            {/* <IconButton
              onClick={undo}
              disabled={disabled || !canUndoValue}
              size={size}
              color="default"
              aria-label={translate("Undo")}
            >
              <UndoIcon fontSize={size} />
            </IconButton> */}
            <IconButton
              className="qadpt-banner-button qadpt-icon"
              onClick={undo}
              disabled={true}
              sx={{ opacity: 0.5 }}
              size={size}
              color="default"
              aria-label={translate("Undo")}
            >
              <span dangerouslySetInnerHTML={{ __html: undoicon }} style={{ width: "24px", placeContent: "center" }}></span>
            </IconButton>
          </span>
        </Tooltip>
      </Box>
      <Box sx={{ marginLeft: 1 }}>
        <Tooltip arrow title={translate("coming soon")}>
          <span>
            {/* <IconButton
              onClick={redo}
              disabled={disabled || !canRedoValue}
              size={size}
              color="default"
              aria-label={translate("Redo")}
            >
              <RedoIcon fontSize={size} />
            </IconButton> */}
            <IconButton
              className="qadpt-banner-button qadpt-icon"
              onClick={redo}
              disabled={true}
              sx={{ opacity: 0.5 }}
              size={size}
              color="default"
              aria-label={translate("Redo")}
            >
              <span dangerouslySetInnerHTML={{ __html: redoicon }} style={{ width: "24px", placeContent: "center" }}></span>
            </IconButton>
          </span>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default UndoRedoButtons;
