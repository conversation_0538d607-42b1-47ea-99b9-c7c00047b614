import React, { useState } from "react";
import { Box, Button, Popover, Typography, TextField, IconButton } from "@mui/material";
import { ChromePicker, ColorResult } from "react-color";
import { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from "../../../assets/icons/icons";
import useDrawerStore, { TButton } from "../../../store/drawerStore";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";

const ButtonSection: React.FC<{ buttonColor: string; setButtonColor: (str: string) => void }> = ({
	buttonColor,
	setButtonColor,
}) => {
	const {
		buttonsContainer,
		cloneButtonContainer,
		updateButton,
		addNewButton,
		deleteButton,
		deleteButtonContainer,
		updateContainer,
		setSettingAnchorEl,
		setCuntainerId,
		setButtonId,
	} = useDrawerStore((state) => state);
	const [isEditingPrevious, setIsEditingPrevious] = useState<boolean>(false);
	const [isEditingContinue, setIsEditingContinue] = useState<boolean>(false);
	const [previousButtonText, setPreviousButtonText] = useState<string>("Previous");
	const [continueButtonText, setContinueButtonText] = useState<string>("Continue");
	const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

	const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);
	const [buttonText, setButtonText] = useState<string>("Continue");
	const [buttonToEdit, setButtonToEdit] = useState<"Previous" | "Continue" | null>(null);
	const [isDeleteIcon, setIsDeleteIcon] = useState("");
	const [currentContainerId, setCurrentContainerId] = useState("");
	const [currentButtonId, setCurrentButtonId] = useState("");
	const [isEditingButton, setIsEditingButton] = useState(false);
	// Default button color
	let clickTimeout: NodeJS.Timeout;
	const handleClick = (event: React.MouseEvent<HTMLElement>, buttonId: string) => {
		const target = event.currentTarget;

		clickTimeout = setTimeout(() => {
			setAnchorEl(target);
			setCurrentButtonId(buttonId);
			setIsEditingButton(false);
			handleEditButtonName(currentContainerId, buttonId, "isEditing", false);
		}, 200);
	};

	const handleClose = () => {
		setAnchorEl(null);
		setButtonToEdit(null);
	};

	const handlePreviousTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setPreviousButtonText(event.target.value);
	};

	const handleContinueTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setContinueButtonText(event.target.value);
	};

	const handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {
		setColorPickerAnchorEl(event.currentTarget);
	};

	const handleColorChange = (color: ColorResult) => {
		// Update the backgroundColor in the container's style
		updateContainer(currentContainerId, "style", {
			backgroundColor: color.hex,
		});

		// Also update the BackgroundColor property at the ButtonSection level
		updateContainer(currentContainerId, "BackgroundColor", color.hex);
	};

	const handleCloseColorPicker = () => {
		setColorPickerAnchorEl(null);
	};

	const open = Boolean(anchorEl);
	// const open = Boolean(anchorEl && !isEditingButton);
	const id = open ? "button-popover" : undefined;
	const colorPickerOpen = Boolean(colorPickerAnchorEl);
	const toggleEdit = (button: "Previous" | "Continue") => {
		if (button === "Previous") {
			setIsEditingPrevious(true);
		} else if (button === "Continue") {
			setIsEditingContinue(true);
		}
	};

	const handlePreviousBlur = () => {
		setIsEditingPrevious(false);
	};

	const handleContinueBlur = () => {
		setIsEditingContinue(false);
	};
	const handleEditButtonName = (
		containerId: string,
		buttonId: string,
		isEditing: keyof TButton,
		value: TButton[keyof TButton]
	) => {
		clearTimeout(clickTimeout);
		setIsEditingButton(true);
		updateButton(containerId, buttonId, isEditing, value);
	};

	const handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {
		updateButton(containerId, buttonId, "type", value);
	};

	const handleAddIconClick = (containerId: string) => {
		addNewButton(
			{
				id: crypto.randomUUID(),
				name: "Button 1",
				position: "center",
				type: "primary",
				isEditing: false,
				index: 0,
				style: {
					backgroundColor: "#5F9EA0",
				},
			},
			containerId
		);
	};

	// shouldShowAddBtn will be calculated per section inside the map
	const currentContainerColor =
		buttonsContainer.find((item) => item.id === currentContainerId)?.style.backgroundColor || "#5f9ea0";
	setButtonId(currentButtonId);
	setCuntainerId(currentButtonId);
	const handleDelteContainer = () => {
		deleteButtonContainer(currentContainerId);
		setAnchorEl(null);
	};

	const handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {
		setSettingAnchorEl({
			containerId: currentContainerId,
			buttonId: currentButtonId,
			// @ts-ignore
			value: event.currentTarget,
		});
		setButtonId(currentButtonId);
		setCuntainerId(currentButtonId);
		setAnchorEl(null);
		handleClose();
	};
	return (
		<>
			{buttonsContainer.map((buttonItem) => {
				return (
					<Box
						component={"div"}
						id={buttonItem.id}
						sx={{
							height: "60px",
							width: "100%",
							display: "flex",
							alignItems: "center",
							gap: "16px",
							padding: "0 16px",
							boxSizing: "border-box",
							//backgroundColor: buttonItem.style.backgroundColor,
							justifyContent: "center",
						}}
						onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}
						// onMouseLeave={(e) => setCurrentContainerId("")}
					>
						{buttonItem.buttons.map((item) => {
							return (
								<Box
									sx={{
										position: "relative",
										display: "flex",
										// flex: 1,
										justifyContent: `center`,
										// "&:hover .edit-icon": { display: "inline-flex" },
									}}
									onMouseLeave={() => {
										setIsDeleteIcon("");
									}}
								>
									<Button
										contentEditable={item.isEditing}
										onMouseOver={(e) => {
											if (item.isEditing === false && e.currentTarget.id === item.id) {
												setIsDeleteIcon(item.id);
											}
										}}
										id={item.id}
										variant="contained"
										sx={{
											borderRadius: "20px",
											borderColor:  "none" ,
											// color: `${item.type !== "primary" ? "#5F9EA0" : "white"}`,
											color: `${item.style.color}`,
											textTransform: "none",
											fontSize: "initial !important",
											backgroundColor: item.style.backgroundColor,
											"&:hover": {
												backgroundColor: "none"
											},
										}}
										onDoubleClick={() => handleEditButtonName(buttonItem.id, item.id, "isEditing", true)}
										onClick={(e) => handleClick(e, item.id)} // Open popover when clicking the button
									>
										{item.name}
									</Button>

									{buttonItem.buttons.length > 1 && isDeleteIcon === item.id ? (
										<IconButton
											size="small"
											// className="edit-icon"
											sx={{
												position: "absolute",
												top: "-10px",
												right: "-8px",
												backgroundColor: "transparent",
												// display: "none", // Initially hidden
												"&:hover": {
													backgroundColor: "rgba(128, 191, 191, 0.2)",
												},
												svg: {
													width: "15px", // Smaller icon size
													height: "15px", // Smaller icon size
												},
											}}
											onClick={() => deleteButton(buttonItem.id, item.id)}
										>
											<span dangerouslySetInnerHTML={{ __html: deleteicon }} />
										</IconButton>
									) : null}
								</Box>
							);
						})}
						{buttonItem.buttons.length < 3 ? (
							<IconButton
								sx={{
									backgroundColor: "#5F9EA0",
									cursor: "pointer",
									zIndex: 1000,
									padding: "8px !important",
									"&:hover": {
										backgroundColor: "#70afaf",
									},
								}}
								// sx={sideAddButtonStyle}
								onClick={() => handleAddIconClick(buttonItem.id)}
							>
								<AddIcon
									fontSize="small"
									sx={{ color: "#fff" }}
								/>
							</IconButton>
						) : null}
					</Box>
				);
			})}
			<Popover
							className="qadpt-bunprop"
				id={id}
				open={open}
				anchorEl={anchorEl}
				onClose={handleClose}
				anchorOrigin={{
					vertical: "top",
					horizontal: "left",
				}}
				transformOrigin={{
					vertical: "bottom",
					horizontal: "left",
				}}
			>
				<Box
					sx={{
						display: "flex",
						alignItems: "center",
						gap: "8px",
						padding: "8px",
					}}
				>
					{/*	<Typography
						variant="body2"
						sx={{ cursor: "pointer", fontWeight: "bold" }}
						component={"div"}
						id="primary"
						onClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}
					>
						Primary
					</Typography>
					<Typography
						variant="body2"
						sx={{ cursor: "pointer", color: "gray" }}
						component={"div"}
						id="secondary"
						onClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}
					>
						Secondary
					</Typography>

					<Box sx={{ borderLeft: "1px solid #ccc", height: "24px", marginLeft: "8px" }}></Box>
   */}
					{/* Icons for additional options */}
					<IconButton
						size="small"
						onClick={handleSettingIconClick}
					>
						<span dangerouslySetInnerHTML={{ __html: settingsicon }} />
					</IconButton>
					<IconButton
						size="small"
						onClick={handleBackgroundColorClick}
					>
						<span dangerouslySetInnerHTML={{ __html: backgroundcoloricon }} />
					</IconButton>
					<IconButton
						size="small"
						onClick={() => cloneButtonContainer(currentContainerId)}
					>
						<span dangerouslySetInnerHTML={{ __html: copyicon }} />
					</IconButton>
					<IconButton
						size="small"
						disabled={buttonsContainer.length === 1}
						onClick={handleDelteContainer}
					>
						<span dangerouslySetInnerHTML={{ __html: deleteicon }} />
					</IconButton>
				</Box>
			</Popover>

			{/* Color Picker Popover */}
			<Popover
				open={colorPickerOpen}
				anchorEl={colorPickerAnchorEl}
				onClose={handleCloseColorPicker}
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "center",
				}}
				transformOrigin={{
					vertical: "top",
					horizontal: "center",
				}}
			>
				<Box>
					<ChromePicker
						color={currentContainerColor}
						onChange={handleColorChange}
					/>
					<style>
						{`
      .chrome-picker input {
        padding: 0 !important;
      }
    `}
					</style>
				</Box>
			</Popover>
		</>
	);
};

export default ButtonSection;
