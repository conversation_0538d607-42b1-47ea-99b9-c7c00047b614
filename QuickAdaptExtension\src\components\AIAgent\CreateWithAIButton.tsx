import React, { useState } from 'react';
import { ai, beta } from '../../assets/icons/icons';
import useDrawerStore, { DrawerState } from "../../store/drawerStore";
import { useTranslation } from 'react-i18next';

interface CreateWithAIButtonProps {
  onClick: () => void;
}

const CreateWithAIButton: React.FC<CreateWithAIButtonProps> = ({ onClick }) => {
  const { t: translate } = useTranslation();
  const {
    isCollapsed,
    setIsCollapsed,
  } = useDrawerStore((state: DrawerState) => state);
  const [isSelected, setIsSelected] = useState(false);

  const handleClick = () => {
    setIsSelected(!isSelected); // toggle active state
    onClick(); // your original click logic
  };
  return (
    <>
      {isCollapsed ? (
        <button
          className={`qadpt-ai-button ${isCollapsed && isSelected ? 'active' : ''}`}
          onClick={onClick}
        >
          <span className="qadpt-aiicon" dangerouslySetInnerHTML={{ __html: ai }} />
        </button>
     
      ) : (
     
        <div className="qadpt-ai-container">
            <div className="beta">{translate("BETA")}</div>
          <div className="qadpt-ai-title">
              {translate("Create interactions using the latest AI technology.")}
          </div>
          <button className="qadpt-button" onClick={onClick}>
            <span className="qadpt-icon" dangerouslySetInnerHTML={{ __html: ai }} />
              <span className="qadpt-text">{translate("Create with AI")}</span>
          </button>
        </div>
       
      )}
       
    </>
  );
  
};

export default CreateWithAIButton;
