import React, { useState } from "react";
import { Box, Typography, IconButton, Popover, TextField, Button } from "@mui/material";
import { Code, Close as CloseIcon } from "@mui/icons-material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import { deleteicon } from "../../../assets/icons/icons";

const HtmlSection: React.FC<{ setHtmlContent: (str: string) => void; htmlContent: string; isBanner: boolean;}> = ({
	setHtmlContent,
	htmlContent,
	isBanner
}) => {
	const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
	const [codePopoverEl, setCodePopoverEl] = useState<HTMLElement | null>(null);
	const [sectionHeight, setSectionHeight] = useState<number>(355); // Changed the initial height to 355px
	const [showSection, setShowSection] = useState<boolean>(true);

	const [inputValue, setInputValue] = useState<string>("");

	const handleSectionClick = (event: React.MouseEvent<HTMLElement>) => {
		event.stopPropagation();
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	const handleChangeCodeClick = (event: React.MouseEvent<HTMLElement>) => {
		event.stopPropagation();
		setCodePopoverEl(event.currentTarget);
		setAnchorEl(null);
	};

	const handleCodePopoverClose = () => {
		setCodePopoverEl(null);
	};

	const handleIncreaseHeight = () => {
		setSectionHeight((prevHeight) => prevHeight + 10);
	};

	const handleDecreaseHeight = () => {
		setSectionHeight((prevHeight) => (prevHeight > 10 ? prevHeight - 10 : prevHeight));
	};

	const handleDeleteSection = () => {
		setShowSection(false);
	};

	const handleSaveCode = () => {
		setHtmlContent(inputValue);
		setCodePopoverEl(null);
	};

	const sectionStyle: React.CSSProperties = {
		width: "100%",
		height: `${sectionHeight}px`,
		display: "flex",
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#f0f0f0",
		position: "relative",
		cursor: "pointer",
	};

	const renderHtmlContent = () => (
		<div
			style={{ width: "100%", height: "100%", overflow: "hidden" }}
			dangerouslySetInnerHTML={{ __html: htmlContent }}
		/>
	);

	return (
		<>
			{showSection && (
				<Box
					sx={sectionStyle}
					onClick={handleSectionClick}
				>
					{htmlContent ? renderHtmlContent() : <Typography variant="body1">HTML Section (Click to Edit)</Typography>}

					{/* First Popover for "Change Code" and height adjustment */}
					<Popover
						open={Boolean(anchorEl)}
						anchorEl={anchorEl}
						onClose={handleClose}
						// anchorOrigin={{
						// 	vertical: "top",
						// 	horizontal: "center",
						// }}
						// transformOrigin={{
						// 	vertical: "bottom",
						// 	horizontal: "center",
						// }}
						PaperProps={{
							style: {
								padding: "12px",
								display: "flex",
								gap: "12px",
								width: "auto",
								// marginTop: "-10px",
								marginTop : isBanner ? "50px" : "-10px"
							},
							className: isBanner ? 'qadpt-html-box' : ''
						}}
					>
						<Box
							sx={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
								height: "100%",
								fontSize: "12px",
							}}
						>
							<Box
								sx={{ display: "flex", alignItems: "center", gap: "8px", fontSize: "12px", cursor: "pointer" }}
								onClick={handleChangeCodeClick}
							>
								<Code />
								<Typography fontSize="12px">Change Code</Typography>
							</Box>

							<Box sx={{ display: "flex", alignItems: "center", gap: "8px", fontSize: "12px" }}>
								<IconButton
									onClick={handleDecreaseHeight}
									size="small"
								>
									<RemoveIcon fontSize="small" />
								</IconButton>
								<Typography fontSize="12px">{sectionHeight}</Typography>
								<IconButton
									onClick={handleIncreaseHeight}
									size="small"
								>
									<AddIcon fontSize="small" />
								</IconButton>
							</Box>

							<Box
								sx={{ display: "flex", alignItems: "center", gap: "8px", fontSize: "12px", cursor: "pointer" }}
								onClick={handleDeleteSection}
							>
								<span dangerouslySetInnerHTML={{ __html: deleteicon }} />
							</Box>
						</Box>
					</Popover>

					{/* Second Popover for editing code */}
					<Popover
						open={Boolean(codePopoverEl)}
						anchorEl={codePopoverEl}
						onClose={handleCodePopoverClose}
						anchorOrigin={{
							vertical: "top",
							horizontal: "right",
						}}
						transformOrigin={{
							vertical: "top",
							horizontal: "left",
						}}
						disablePortal
						keepMounted
						PaperProps={{
							style: {
								width: "200px",
								height: "350px",
								padding: "16px",
								display: "flex",
								flexDirection: "column",
								gap: "16px",
								zIndex: 1300,
								marginLeft: "20px",
							},
						}}
					>
						<div
							onClick={(e) => e.stopPropagation()}
							style={{ height: "100%", display: "flex", flexDirection: "column" }}
						>
							<Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "8px" }}>
								<Typography
									variant="h6"
									fontSize="16px"
								>
									Add HTML Code
								</Typography>
								<IconButton
									size="small"
									onClick={handleCodePopoverClose}
								>
									<CloseIcon fontSize="small" />
								</IconButton>
							</Box>
							<TextField
								placeholder="Enter HTML/iFrame code"
								multiline
								rows={8}
								fullWidth
								variant="outlined"
								value={inputValue}
								onChange={(e) => setInputValue(e.target.value)}
								sx={{ marginBottom: "16px" }}
								onClick={(e) => e.stopPropagation()}
							/>
							<Button
								variant="contained"
								fullWidth
								onClick={handleSaveCode}
								sx={{ backgroundColor: "#5F9EA0", color: "#fff", "&:hover": { backgroundColor: "#70afaf" } }}
							>
								Save
							</Button>
						</div>
					</Popover>
				</Box>
			)}
		</>
	);
};

export default HtmlSection;
