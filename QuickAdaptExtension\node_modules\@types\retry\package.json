{"name": "@types/retry", "version": "0.12.2", "description": "TypeScript definitions for retry", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/retry", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/krenor", "githubUsername": "krenor"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/retry"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "7e04c0f9ea44e56f6cd542dda085887cda53dcb0533e4d3fd938bee5463b5b9e", "typeScriptVersion": "3.9"}