import React, { useState } from 'react';
import {
  Select,
  MenuItem,
  FormControl,
  Box,
  Typography,
  SelectChangeEvent,
  Tooltip,
  IconButton,
} from '@mui/material';
import { Language as LanguageIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import useInfoStore from '../../store/UserInfoStore';
import { useTranslationContext } from '../../contexts/TranslationContext';

interface LanguageSelectorProps {
  variant?: 'select' | 'icon';
  size?: 'small' | 'medium';
  showLabel?: boolean;
  className?: string;
}

const LANGUAGE_STORAGE_KEY_PREFIX = 'quickadapt_language_';

function getOrgLanguageKey(orgId: string | undefined) {
  return `${LANGUAGE_STORAGE_KEY_PREFIX}${orgId || 'default'}`;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'select',
  size = 'small',
  showLabel = false,
  className = '',
}) => {
  const { t: translate } = useTranslation();
  const { availableLanguages, currentLanguage, changeLanguage, isLoading, isInitialized } = useTranslationContext();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [localLoading, setLocalLoading] = useState(false);

  const orgId = useInfoStore((state) => state.orgDetails?.OrganizationId);

  // Don't render if not initialized or no languages available
  if (!isInitialized || availableLanguages.length === 0) {
    return null;
  }


  // Sort languages alphabetically by their display name
  const sortedLanguages = [...availableLanguages].sort((a, b) =>
    a.Language.localeCompare(b.Language)
  );

  // Ensure we have a valid current language
  const validCurrentLanguage = sortedLanguages.find(
    lang => lang.LanguageCode.toLowerCase() === currentLanguage.toLowerCase()
  ) ? currentLanguage : sortedLanguages[0]?.LanguageCode || 'en';

  const handleLanguageChange = async (event: SelectChangeEvent<string>) => {
    const newLanguageCode = event.target.value;
    if (newLanguageCode === validCurrentLanguage) return;

    setLocalLoading(true);
    try {
      await changeLanguage(newLanguageCode);
      // Language saving is now handled in the i18n module
    } catch (error) {
      console.error('Language change failed:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  const handleIconClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = async (languageCode: string) => {
    if (languageCode === validCurrentLanguage) {
      handleClose();
      return;
    }

    setLocalLoading(true);
    try {
      await changeLanguage(languageCode);
      // Language saving is now handled in the i18n module
    } catch (error) {
      console.error('Language change failed:', error);
    } finally {
      setLocalLoading(false);
      handleClose();
    }
  };

  if (variant === 'icon') {
    return (
      <>
        <Tooltip title={translate('Change Language')}>
          <IconButton
            onClick={handleIconClick}
            size={size}
            className={className}
            disabled={isLoading || localLoading}
          >
            <LanguageIcon sx={{height :"22px" , width : "22px"}} />
          </IconButton>
        </Tooltip>
        <Select
          open={Boolean(anchorEl)}
          onClose={handleClose}
          value={validCurrentLanguage}
          MenuProps={{
            anchorEl,
            open: Boolean(anchorEl),
            onClose: handleClose,
            anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
            transformOrigin: { vertical: 'top', horizontal: 'right' },
          }}
          sx={{ display: 'none' }}
        >
          {sortedLanguages.map((lang) => (
            <MenuItem
              key={lang.LanguageId}
              value={lang.LanguageCode}
              onClick={() => handleMenuItemClick(lang.LanguageCode)}
              selected={lang.LanguageCode === validCurrentLanguage}
            >
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2">{lang.Language}</Typography>
                {lang.LanguageCode === validCurrentLanguage && (
                  <Typography variant="caption" color="primary">
                    (Current)
                  </Typography>
                )}
              </Box>
            </MenuItem>
          ))}
        </Select>
      </>
    );
  }

  return (
    <FormControl size={size} className={className}>
      {showLabel && (
        <Typography variant="caption" sx={{ mb: 0.5 }}>
          {translate('Language')}
        </Typography>
      )}
      <Select
        value={validCurrentLanguage}
        onChange={handleLanguageChange}
        disabled={isLoading || localLoading}
        sx={{
          minWidth: 120,
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          },
        }}
      >
        {sortedLanguages.map((lang) => (
          <MenuItem key={lang.LanguageId} value={lang.LanguageCode}>
            <Box display="flex" alignItems="center" gap={1}>
              <Typography variant="body2">{lang.Language}</Typography>
              {lang.LanguageCode === validCurrentLanguage && (
                <Typography variant="caption" color="primary">
                  (Current)
                </Typography>
              )}
            </Box>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default LanguageSelector;
