import { adminApiService } from './APIService';

export const CreateInteraction = async (userCommand: string, accountId: string,targetUrl:string) => {
    
    try {
        const requestBody = {
			userCommand,
			accountId,
			targetUrl,
		};
        const response = await adminApiService.post(`Ai/CreateInteraction`, requestBody)
        return response.data;
    } catch (error) {
        console.error("Error in creating integration", error);
        return [];
    }
}