{"name": "@types/react-color", "version": "3.0.12", "description": "TypeScript definitions for react-color", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-color", "license": "MIT", "contributors": [{"name": " <PERSON><PERSON>", "githubUsername": "LKay", "url": "https://github.com/LKay"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/markspolakovs"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "mntdn", "url": "https://github.com/mntdn"}, {"name": "Nokogiri", "githubUsername": "nkgrnkgr", "url": "https://github.com/nkgrnkgr"}, {"name": "0815Strohhut", "githubUsername": "0815Strohhut", "url": "https://github.com/0815Strohhut"}, {"name": "<PERSON>", "githubUsername": "dnlfrst", "url": "https://github.com/dnlfrst"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ericktamayo"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/alexandercerutti"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-color"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/reactcss": "*"}, "typesPublisherContentHash": "cf28bee9e29641358f75873c69058fafb466a308b10f5d20fe249243e1e4e8c0", "typeScriptVersion": "4.6"}