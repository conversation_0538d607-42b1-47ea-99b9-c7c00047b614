{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PageTrigger.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box, Typography, TextField, Button, IconButton, Tooltip } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\n// import Draggable from \"react-draggable\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageTrigger = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(true);\n  const [timedDelay, setTimedDelay] = useState(\"\");\n  const [onScroll, setOnScroll] = useState(\"\");\n  const handleClose = () => {\n    setIsOpen(false);\n  };\n  const handleTimedDelayChange = e => {\n    const value = e.target.value;\n    if (/^\\d*$/.test(value)) {\n      // Regex to allow only digits\n      setTimedDelay(value);\n    }\n  };\n  const handleOnScrollChange = e => {\n    const value = e.target.value;\n    if (/^\\d*$/.test(value)) {\n      // Regex to allow only digits\n      setOnScroll(value);\n    }\n  };\n  if (!isOpen) return null;\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: \"Page Trigger\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              children: \"Timed Delay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-input-box\",\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                value: timedDelay,\n                onChange: handleTimedDelayChange,\n                variant: \"outlined\",\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                inputProps: {\n                  style: {\n                    textAlign: \"center\"\n                  },\n                  endAdornment: \"px\",\n                  type: \"number\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              children: \"On Scroll\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-input-box\",\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                value: onScroll,\n                onChange: handleOnScrollChange,\n                variant: \"outlined\",\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                inputProps: {\n                  style: {\n                    textAlign: \"center\"\n                  },\n                  endAdornment: \"px\",\n                  type: \"number\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            backgroundColor: \"var(--back-light-color)\",\n            borderRadius: \"10px\",\n            padding: \"12px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              fontSize: \"14px\",\n              marginBottom: \"8px\",\n              color: \"rgba(0, 0, 0, 0.38)\"\n            },\n            children: \"Click Element\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            arrow: true,\n            title: \"Coming Soon\",\n            PopperProps: {\n              sx: {\n                zIndex: 9999\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                disabled: true,\n                variant: \"outlined\",\n                sx: {\n                  width: \"100%\",\n                  textTransform: \"none\",\n                  borderRadius: \"8px\",\n                  borderColor: \"var(--border-color)\",\n                  color: \"var(--primarycolor)\"\n                },\n                children: \"Choose element\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(PageTrigger, \"x08GrTRHZbcnmydXH1RoSOZN/SU=\");\n_c = PageTrigger;\nexport default PageTrigger;\nvar _c;\n$RefreshReg$(_c, \"PageTrigger\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "TextField", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "CloseIcon", "jsxDEV", "_jsxDEV", "PageTrigger", "_s", "isOpen", "setIsOpen", "timedDelay", "setTimedDelay", "onScroll", "setOnScroll", "handleClose", "handleTimedDelayChange", "e", "value", "target", "test", "handleOnScrollChange", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "onChange", "variant", "inputProps", "style", "textAlign", "endAdornment", "type", "sx", "backgroundColor", "borderRadius", "padding", "fontSize", "marginBottom", "color", "arrow", "title", "PopperProps", "zIndex", "disabled", "width", "textTransform", "borderColor", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PageTrigger.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Box, Typography, TextField, Button, IconButton, Tooltip } from \"@mui/material\";\r\nimport ArrowBackIosIcon from \"@mui/icons-material/ArrowBackIos\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\n// import Draggable from \"react-draggable\";\r\n\r\nconst PageTrigger = () => {\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [timedDelay, setTimedDelay] = useState(\"\");\r\n\tconst [onScroll, setOnScroll] = useState(\"\");\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t};\r\n\r\n\tconst handleTimedDelayChange = (e: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tif (/^\\d*$/.test(value)) {\r\n\t\t\t// Regex to allow only digits\r\n\t\t\tsetTimedDelay(value);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleOnScrollChange = (e: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tif (/^\\d*$/.test(value)) {\r\n\t\t\t// Regex to allow only digits\r\n\t\t\tsetOnScroll(value);\r\n\t\t}\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">Page Trigger</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t{/* Timed Delay Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Timed Delay</Typography>\r\n\t\t\t\t\t\t<Box className=\"qadpt-input-box\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={timedDelay}\r\n\t\t\t\t\t\t\t\tonChange={handleTimedDelayChange}\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tinputProps={{\r\n\t\t\t\t\t\t\t\t\tstyle: { textAlign: \"center\" },\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\ttype: \"number\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t{/* On Scroll Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">On Scroll</Typography>\r\n\t\t\t\t\t\t<Box className=\"qadpt-input-box\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={onScroll}\r\n\t\t\t\t\t\t\t\tonChange={handleOnScrollChange}\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tinputProps={{\r\n\t\t\t\t\t\t\t\t\tstyle: { textAlign: \"center\" },\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\ttype: \"number\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"var(--back-light-color)\",\r\n\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", marginBottom: \"8px\", color: \"rgba(0, 0, 0, 0.38)\" }}>\r\n\t\t\t\t\t\tClick Element\r\n\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\tPopperProps={{ sx: { zIndex: 9999 } }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tChoose element\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t</Box>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default PageTrigger;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAEvF,OAAOC,SAAS,MAAM,2BAA2B;AACjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMkB,WAAW,GAAGA,CAAA,KAAM;IACzBL,SAAS,CAAC,KAAK,CAAC;EACjB,CAAC;EAED,MAAMM,sBAAsB,GAAIC,CAAM,IAAK;IAC1C,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B,IAAI,OAAO,CAACE,IAAI,CAACF,KAAK,CAAC,EAAE;MACxB;MACAN,aAAa,CAACM,KAAK,CAAC;IACrB;EACD,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,CAAM,IAAK;IACxC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B,IAAI,OAAO,CAACE,IAAI,CAACF,KAAK,CAAC,EAAE;MACxB;MACAJ,WAAW,CAACI,KAAK,CAAC;IACnB;EACD,CAAC;EAED,IAAI,CAACT,MAAM,EAAE,OAAO,IAAI;EAExB;IAAA;IACC;IACAH,OAAA;MACCgB,EAAE,EAAC,mBAAmB;MACtBC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAE7BlB,OAAA;QAAKiB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC7BlB,OAAA;UAAKiB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnClB,OAAA;YAAKiB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CtB,OAAA,CAACJ,UAAU;YACV2B,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBC,OAAO,EAAEf,WAAY;YAAAS,QAAA,eAErBlB,OAAA,CAACF,SAAS;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAE9BlB,OAAA,CAACR,GAAG;YAACyB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjClB,OAAA,CAACP,UAAU;cAACwB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpEtB,OAAA,CAACR,GAAG;cAACyB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC/BlB,OAAA,CAACN,SAAS;gBACTkB,KAAK,EAAEP,UAAW;gBAClBoB,QAAQ,EAAEf,sBAAuB;gBACjCgB,OAAO,EAAC,UAAU;gBAClBH,IAAI,EAAC,OAAO;gBACZN,SAAS,EAAC,qBAAqB;gBAC/BU,UAAU,EAAE;kBACXC,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAS,CAAC;kBAC9BC,YAAY,EAAE,IAAI;kBAClBC,IAAI,EAAE;gBACP;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGNtB,OAAA,CAACR,GAAG;YAACyB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjClB,OAAA,CAACP,UAAU;cAACwB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClEtB,OAAA,CAACR,GAAG;cAACyB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC/BlB,OAAA,CAACN,SAAS;gBACTkB,KAAK,EAAEL,QAAS;gBAChBkB,QAAQ,EAAEV,oBAAqB;gBAC/BW,OAAO,EAAC,UAAU;gBAClBH,IAAI,EAAC,OAAO;gBACZN,SAAS,EAAC,qBAAqB;gBAC/BU,UAAU,EAAE;kBACXC,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAS,CAAC;kBAC9BC,YAAY,EAAE,IAAI;kBAClBC,IAAI,EAAE;gBACP;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENtB,OAAA,CAACR,GAAG;UACHwC,EAAE,EAAE;YACHC,eAAe,EAAE,yBAAyB;YAC1CC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE;UACV,CAAE;UAAAjB,QAAA,gBAEFlB,OAAA,CAACP,UAAU;YAACuC,EAAE,EAAE;cAAEI,QAAQ,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEC,KAAK,EAAE;YAAsB,CAAE;YAAApB,QAAA,EAAC;UAEzF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbtB,OAAA,CAACH,OAAO;YAAC0C,KAAK;YACbC,KAAK,EAAC,aAAa;YACnBC,WAAW,EAAE;cAAET,EAAE,EAAE;gBAAEU,MAAM,EAAE;cAAK;YAAE,CAAE;YAAAxB,QAAA,eAEtClB,OAAA;cAAAkB,QAAA,eACClB,OAAA,CAACL,MAAM;gBACNgD,QAAQ;gBACRjB,OAAO,EAAC,UAAU;gBAClBM,EAAE,EAAE;kBACHY,KAAK,EAAE,MAAM;kBACbC,aAAa,EAAE,MAAM;kBACrBX,YAAY,EAAE,KAAK;kBACnBY,WAAW,EAAE,qBAAqB;kBAClCR,KAAK,EAAE;gBACR,CAAE;gBAAApB,QAAA,EACF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;IACL;EAAA;AAEF,CAAC;AAACpB,EAAA,CAzHID,WAAW;AAAA8C,EAAA,GAAX9C,WAAW;AA2HjB,eAAeA,WAAW;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}