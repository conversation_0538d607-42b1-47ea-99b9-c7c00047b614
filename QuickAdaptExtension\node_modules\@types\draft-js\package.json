{"name": "@types/draft-js", "version": "0.11.18", "description": "TypeScript definitions for draft-js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/draft-js", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/dmit<PERSON><PERSON>ozhny"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "eelco", "url": "https://github.com/eelco"}, {"name": "Yale Cason", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ghotiphud"}, {"name": "<PERSON>", "githubUsername": "schwers", "url": "https://github.com/schwers"}, {"name": "<PERSON>", "githubUsername": "mi<PERSON><PERSON>-yx-wu", "url": "https://github.com/michael-yx-wu"}, {"name": "<PERSON>", "githubUsername": "willis<PERSON><PERSON>mer", "url": "https://github.com/willisplummer"}, {"name": "Santiago Vilar", "githubUsername": "sm<PERSON><PERSON>", "url": "https://github.com/smvilar"}, {"name": "<PERSON><PERSON>", "githubUsername": "sulf", "url": "https://github.com/sulf"}, {"name": "<PERSON>", "githubUsername": "pab<PERSON><PERSON>", "url": "https://github.com/pablopunk"}, {"name": "<PERSON>", "githubUsername": "clau<PERSON><PERSON><PERSON>", "url": "https://github.com/claudiopro"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/k<PERSON>inson"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "MunifTanjim", "url": "https://github.com/MunifTanjim"}, {"name": "<PERSON>", "githubUsername": "PeterDek<PERSON>", "url": "https://github.com/PeterDekkers"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ankitr"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/draft-js"}, "scripts": {}, "dependencies": {"@types/react": "*", "immutable": "~3.7.4"}, "typesPublisherContentHash": "f231f944671c2f3b7d66d59b7e0143e6b53fa1426e86042ada9f6304cdbca942", "typeScriptVersion": "4.6"}