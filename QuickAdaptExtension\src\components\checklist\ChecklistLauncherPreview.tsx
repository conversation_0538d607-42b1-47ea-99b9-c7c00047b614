
import React, { useEffect, useMemo, useState } from 'react';
import useDrawerStore from '../../store/drawerStore';
import ChecklistPopup from "./ChecklistPopup";
import ChecklistPreview from "./ChecklistPreview";
import '../../styles/rtl_styles.scss';

import { chkcloseicon, chkicn1, closeicon, closepluginicon } from '../../assets/icons/icons';
const ChecklistLauncher = () => {
	const { checklistGuideMetaData, setShowLauncherSettings, showLauncherSettings } = useDrawerStore(
		(state: any) => state
	);
	const [isOpen, setIsOpen] = useState(false);
	const [remainingCount, setRemainingCount] = useState("00"); // Initial value - will be updated with actual count

	// Callback function to receive the formatted remaining count
	const handleRemainingCountUpdate = (formattedCount: string) => {
		console.log("Launcher received count update:", formattedCount);
		setRemainingCount(formattedCount);
	};

	// Check localStorage for remainingCount updates
	useEffect(() => {
		const handleStorageChange = (e: StorageEvent) => {
			if (e.key === "remainingCount") {
				console.log("Launcher detected storage change for remainingCount:", e.newValue);
				if (e.newValue) {
					setRemainingCount(e.newValue);
				}
			}
		};

		// Set up event listener for storage changes
		window.addEventListener("storage", handleStorageChange);

		// Check for initial value
		const storedCount = window.localStorage.getItem("remainingCount");
		if (storedCount) {
			console.log("Launcher initial count from localStorage:", storedCount);
			setRemainingCount(storedCount);
		}

		// Set up interval to check localStorage periodically
		const intervalId = setInterval(() => {
			const currentCount = window.localStorage.getItem("remainingCount");
			if (currentCount && currentCount !== remainingCount) {
				console.log("Launcher periodic check found new count:", currentCount);
				setRemainingCount(currentCount);
			}
		}, 1000); // Check every second

		// Clean up event listener and interval
		return () => {
			window.removeEventListener("storage", handleStorageChange);
			clearInterval(intervalId);
		};
	}, [remainingCount]);
	const [isRightPanelVisible, setIsRightPanelVisible] = useState(false); // Track right panel visibility
	const [icons, setIcons] = useState<any[]>([
		{
			id: 1,
			component: (
				<span
					dangerouslySetInnerHTML={{ __html: chkicn1 }}
					style={{ zoom: 1, display: "flex" }}
				/>
			),
			selected: true,
		},
	]);
	const iconColor = checklistGuideMetaData[0]?.launcher?.iconColor || "#fff"; // Default to black if no color
	const base64IconFinal = checklistGuideMetaData[0]?.launcher?.icon;

	let base64Icon: any;
	const encodeToBase64 = (svgString: string) => {
		return `data:image/svg+xml;base64,${btoa(svgString)}`;
	};
	const initialSelectedIcon = icons.find((icon) => icon.selected);
	if (initialSelectedIcon) {
		const svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;

		if (svgElement) {
			base64Icon = encodeToBase64(svgElement);
		}
	}
	const modifySVGColor = (base64SVG: any, color: any) => {
		if (!base64SVG) {
			return "";
		}

		try {
			// Check if the string is a valid base64 SVG
			if (!base64SVG.includes("data:image/svg+xml;base64,")) {
				return base64SVG; // Return the original if it's not an SVG
			}

			const decodedSVG = atob(base64SVG.split(",")[1]);

			// Check if this is primarily a stroke-based or fill-based icon
			const hasStroke = decodedSVG.includes('stroke="');
			const hasColoredFill = /fill="(?!none)[^"]+"/g.test(decodedSVG);

			let modifiedSVG = decodedSVG;

			if (hasStroke && !hasColoredFill) {
				// This is a stroke-based icon (like chkicn2-6) - only change stroke color
				modifiedSVG = modifiedSVG.replace(/stroke="[^"]+"/g, `stroke="${color}"`);
			} else if (hasColoredFill) {
				// This is a fill-based icon (like chkicn1) - only change fill color
				modifiedSVG = modifiedSVG.replace(/fill="(?!none)[^"]+"/g, `fill="${color}"`);
			} else {
				// No existing fill or stroke, add fill to make it visible
				modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill="${color}"`);
				modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill="${color}"`);
			}

			const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;
			return modifiedBase64;
		} catch (error) {
			console.error("Error modifying SVG color:", error);
			return base64SVG; // Return the original if there's an error
		}
	};
	const checklistColor = checklistGuideMetaData[0]?.canvas?.primaryColor;

	const modifiedIcon = modifySVGColor(base64IconFinal ? base64IconFinal : base64Icon, iconColor);
	// Get offset values from launcher settings, with fallback to default values
	const xOffset = checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || "10";
	const yOffset = checklistGuideMetaData[0]?.launcher?.launcherposition?.yaxisOffset || "10";
	const isLeft = checklistGuideMetaData[0]?.launcher?.launcherposition?.left === true;
	

	return (
		<div>
			<div
  style={{
    '--y-offset': `${yOffset}px`,
    '--x-offset': `${xOffset}px`
  } as any } 
  className={`qadpr-chkprvlayout ${isLeft ? 'left' : 'right'}`}
>
				<button
					onClick={() => {
						setIsOpen((prev) => !prev);
						setIsRightPanelVisible(true);
					}}
					style={{
						backgroundColor: checklistGuideMetaData[0]?.launcher.launcherColor,
						color: "white",
						borderRadius:
							checklistGuideMetaData[0]?.launcher.type === "Text" ||
							checklistGuideMetaData[0]?.launcher.type === "Icon+Txt"
								? "16px"
								: "50%",
						height: "54px",
						width:
							checklistGuideMetaData[0]?.launcher.type === "Text" ||
							checklistGuideMetaData[0]?.launcher.type === "Icon+Txt"
								? `auto`
								: "54px",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						padding: "8px",
						boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
						transition: "all 0.2s ease",
						border: "none",
						cursor: "pointer",
						position: "relative",
					}}
				>
					{isOpen ? (
						<span
							dangerouslySetInnerHTML={{ __html: chkcloseicon }}
							style={{
								borderRadius: "50%",
								padding: "8px",
								display: "flex",
								cursor: "pointer",
								color: "white",
								stroke: "white",
								fill: "white",
							}}
						/>
					) : (
						<>
							{checklistGuideMetaData[0]?.launcher?.type === "Icon" && (
								<img
									src={modifiedIcon}
									alt="icon"
									style={{ width: "20px", height: "20px" }}
								/>
							)}

							{checklistGuideMetaData[0]?.launcher?.type === "Text" && checklistGuideMetaData[0]?.launcher?.text && (
								<span
									style={{
										fontSize: "16px",
										fontWeight: "bold",
										color: checklistGuideMetaData[0]?.launcher?.textColor,
										padding: "8px",
										whiteSpace: "nowrap",
									}}
								>
									{checklistGuideMetaData[0].launcher.text}
								</span>
							)}

							{checklistGuideMetaData[0]?.launcher?.type === "Icon+Txt" &&
								checklistGuideMetaData[0]?.launcher?.text && (
									<span
										style={{
											display: "flex",
											alignItems: "center",
											gap: "8px",
											color: checklistGuideMetaData[0]?.launcher?.textColor,
											fontSize: "16px",
											fontWeight: "bold",
											padding: "8px",
										}}
									>
										<img
											src={modifiedIcon}
											alt="icon"
											style={{ width: "20px", height: "20px" }}
										/>
										{checklistGuideMetaData[0]?.launcher?.text}
									</span>
								)}
						</>
					)}

					{/* Notification Badge */}
					{checklistGuideMetaData[0]?.launcher?.notificationBadge && (
						<div
							style={{
								position: "absolute",
								top: "-8px",
								right: "-8px",
								backgroundColor: checklistGuideMetaData[0]?.launcher?.notificationBadgeColor,
								color: checklistGuideMetaData[0]?.launcher?.notificationTextColor,
								fontSize: "12px",
								borderRadius: "9999px",
								height: "24px",
								width: "24px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
						>
							{/* Display the remaining count (number of incomplete steps) */}
							{(window.localStorage && window.localStorage.getItem("remainingCount")) || remainingCount}
						</div>
					)}
				</button>
			</div>

			<ChecklistPreview
				data={""}
				guideDetails={""}
				isRightPanelVisible={isRightPanelVisible}
				setIsRightPanelVisible={setIsRightPanelVisible}
				isOpen={isOpen}
				onClose={() => setIsOpen(false)}
				onRemainingCountUpdate={handleRemainingCountUpdate}
			/>
		</div>
	);
};
export default ChecklistLauncher;