# Test Plan: Data Binding Fix for Multi-Step Tours

## Issue Description
When creating multi-step tours with different step types (Announcement vs Banner), RTE content and button configurations from Step 1 were incorrectly appearing in Step 2 after saving, indicating improper data isolation between step types.

## Root Cause Analysis
1. **Shared Data Structure Issue**: Both step types used `toolTipGuideMetaData` but lacked proper step isolation
2. **Synchronization Timing**: `syncCurrentStepDataForAITour()` was causing cross-contamination between steps
3. **Reference Issues**: Direct object references were causing data to leak between steps
4. **Step Creation Timing**: Navigation to newly created steps happened before metadata was fully initialized

## Fixes Applied

### 1. Step Data Isolation (`syncCurrentStepDataForAITour`)
- Added deep copying to prevent reference contamination
- Ensured each step only updates its own data in `interactionData`
- Added comprehensive logging for debugging
- Fixed TextFieldProperties and ButtonSection isolation

### 2. RTE Data Binding (`handleTooltipRTEValue`)
- Added validation for metadata existence
- Implemented proper step isolation for RTE updates
- Added deep copying for interactionData updates
- Enhanced error handling and logging

### 3. Step Creation and Navigation Timing
- Added setTimeout to ensure step creation completes before navigation
- Enhanced validation in `createNewTooltipSteps`
- Added step existence validation in `handleStepChange`
- Improved metadata initialization logging

## Test Scenarios

### Test 1: Basic Multi-Step Tour Creation
1. Create a new tour
2. Add Step 1 as Announcement type
3. Add RTE content: "This is Step 1 Announcement content"
4. Add button with text "Step 1 Button"
5. Add Step 2 as Banner type
6. Add RTE content: "This is Step 2 Banner content"
7. Add button with text "Step 2 Button"
8. Save the tour
9. **Expected**: Each step should retain its own content

### Test 2: Step Navigation and Data Persistence
1. Create tour with 2 steps (Announcement + Banner)
2. Add content to Step 1
3. Navigate to Step 2
4. Add different content to Step 2
5. Navigate back to Step 1
6. **Expected**: Step 1 content should be preserved
7. Navigate to Step 2 again
8. **Expected**: Step 2 content should be preserved

### Test 3: Immediate Navigation After Step Creation
1. Create a tour
2. Add Step 1 with content
3. Create Step 2 and immediately navigate to it
4. **Expected**: Navigation should work without errors
5. **Expected**: Step 2 should have default empty content

### Test 4: Save and Reload Verification
1. Create multi-step tour with different content per step
2. Save the tour
3. Reload/refresh the extension
4. **Expected**: All step content should be preserved correctly

## Validation Points

### Data Isolation Checks
- [ ] Step 1 RTE content doesn't appear in Step 2
- [ ] Step 2 RTE content doesn't appear in Step 1
- [ ] Button configurations are step-specific
- [ ] Canvas settings are step-specific

### Navigation Checks
- [ ] Newly created steps can be navigated to immediately
- [ ] No console errors during step creation and navigation
- [ ] Metadata exists for all created steps

### Persistence Checks
- [ ] Data survives step navigation
- [ ] Data survives save operations
- [ ] Data survives preview mode transitions

## Console Logging
The fixes include comprehensive console logging with prefixes:
- `✅` for successful operations
- `❌` for errors
- `⚠️` for warnings
- `🔄` for synchronization operations

## Expected Console Output
When creating and navigating steps, you should see:
```
✅ createNewTooltipSteps: Setting newstepid to: [uuid] for step: Step 2
✅ createNewTooltipSteps: Successfully created tooltip metadata
🔄 Navigating to newly created step: [uuid]
✅ handleStepChange: Navigating to step: {id, stepName, stepType, stepCount}
✅ handleStepChange: Tooltip metadata exists for step [index]
```

## Regression Testing
After applying fixes, verify:
1. Existing single-step guides still work
2. AI-created tours still function properly
3. Manual tour creation still works
4. Preview mode still functions correctly
5. Save/load operations work as expected
