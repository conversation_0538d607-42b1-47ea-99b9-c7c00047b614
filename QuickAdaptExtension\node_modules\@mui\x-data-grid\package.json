{"name": "@mui/x-data-grid", "version": "7.20.0", "description": "The Community plan edition of the Data Grid components (MUI X).", "author": "MUI Team", "main": "./node/index.js", "license": "MIT", "bugs": {"url": "https://github.com/mui/mui-x/issues"}, "homepage": "https://mui.com/x/react-data-grid/", "sideEffects": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "publishConfig": {"access": "public", "directory": "build"}, "keywords": ["react", "react-component", "material-ui", "mui", "mui-x", "react-table", "table", "datatable", "data-table", "datagrid", "data-grid"], "repository": {"type": "git", "url": "git+https://github.com/mui/mui-x.git", "directory": "packages/x-data-grid"}, "dependencies": {"@babel/runtime": "^7.25.7", "@mui/utils": "^5.16.6 || ^6.0.0", "clsx": "^2.1.1", "prop-types": "^15.8.1", "reselect": "^5.1.1", "@mui/x-internals": "7.20.0"}, "peerDependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/material": "^5.15.14 || ^6.0.0", "@mui/system": "^5.15.14 || ^6.0.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "engines": {"node": ">=14.0.0"}, "private": false, "module": "./index.js", "types": "./index.d.ts"}