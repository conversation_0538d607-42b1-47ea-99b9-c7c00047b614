.ai-options-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
  }

  .ai-options-popup {
    background: white;
    border-radius: 12px;
    padding: 24px;
    width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-height: 80vh;
    overflow-y: auto;
  }

  .ai-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  .ai-options-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
  }

  .close-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 4px;
  }

  .ai-options-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
  }

  .scraping-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 8px;
    padding: 12px;
    border: 1px solid #eee;
    border-radius: 8px;
    background-color: #f9f9f9;
  }

  .scraping-option {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
  }

  .scraping-option input[type="checkbox"] {
    margin: 0;
  }

  .option-button {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s;
  }

  .option-button:hover {
    background: #f5f5f5;
    border-color: #5F9EA0;
  }

  .upload-button {
    cursor: pointer;
  }

  .option-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .start-scrap-button {
    width: 100%;
    padding: 12px;
    background-color: #5F9EA0;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .start-scrap-button:hover {
    background-color: #4F8E90;
  }

  /* New styles for file list */
  .file-list {
    margin-top: 16px;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 16px;
    background-color: #f9f9f9;
  }

  .file-list h4 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 16px;
    color: #333;
  }

  .file-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
  }

  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: white;
    border-radius: 4px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .file-name {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 250px;
  }

  .remove-file-btn {
    background: none;
    border: none;
    color: #999;
    font-size: 18px;
    cursor: pointer;
    padding: 0 4px;
  }

  .remove-file-btn:hover {
    color: #ff5252;
  }

  .upload-submit-button {
    width: 100%;
    padding: 12px;
    background-color: #5F9EA0;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    margin-top: 16px;
    transition: background-color 0.3s;
  }

  .upload-submit-button:hover {
    background-color: #4F8E90;
  }

  /* Styles for scraping section */
  .scraping-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    border: 1px solid #eee;
    border-radius: 8px;
    background-color: #f9f9f9;
  }

  .uploaded-file-info {
    text-align: center;
    margin-bottom: 8px;
  }

  .uploaded-file-info h4 {
    margin: 0 0 8px 0;
    color: #5F9EA0;
    font-size: 18px;
  }

  .uploaded-file-info p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }

  .scraping-button {
    background-color: #5F9EA0;
    color: white;
    border: none;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .scraping-button:hover {
    background-color: #4F8E90;
  }