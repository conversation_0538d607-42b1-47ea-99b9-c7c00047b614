{"name": "jodit-react", "version": "5.2.19", "description": "<PERSON><PERSON> is awesome and usefully wysiwyg editor with filebrowser", "main": "build/jodit-react.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://xdsoft.net/)", "keywords": ["react", "jodit", "html", "text", "editor", "wysisyg", "rich editor", "rich text editor", "rte"], "dependencies": {"jodit": "^4.6.2"}, "peerDependencies": {"react": "~0.14 || ^15 || ^16 || ^17 || ^18 || ^19", "react-dom": "~0.14 || ^15 || ^16 || ^17 || ^18 || ^19"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@jest/globals": "^29.7.0", "@swc-node/register": "^1.10.9", "@swc/core": "^1.10.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.0", "@types/jest": "^29.5.14", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.20.0", "css-loader": "^7.1.2", "eslint": "9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "replace": "^1.2.2", "style-loader": "^4.0.0", "swc-loader": "^0.2.6", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.3", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0"}, "scripts": {"newversion": "npm version patch --no-git-tag-version && npm run github", "lint": "npm run lint:ts && npm run lint:eslint", "lint:ts": "tsc --noEmit", "lint:eslint": "eslint ./", "demo": "NODE_ENV=development node -r @swc-node/register ./node_modules/.bin/webpack serve --config ./examples/webpack.config.ts --mode development", "start": "npm run demo", "build": "npm run build:react && npm run build:types", "build:react": "NODE_ENV=production node -r @swc-node/register ./node_modules/.bin/webpack --mode production", "build:types": "rm -rf ./build/types && tsc --project tsconfig.types.json && npm run remove-css", "remove-css": "replace \"import '[^']+.css';\" '' ./build/**/*.ts", "github": "npm run git && git push --tags origin HEAD:main", "git": "git add --all  && git commit -m \"New version $npm_package_version. Read more https://github.com/jodit/jodit-react/releases/tag/$npm_package_version \" && git tag $npm_package_version", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/jodit/jodit-react.git"}, "license": "MIT", "bugs": {"url": "https://github.com/jodit/jodit-react/issues"}, "homepage": "https://xdsoft.net/jodit/", "ava": {"extensions": ["ts", "tsx"], "require": ["@swc-node/register"], "files": ["packages/**/*.spec.{ts,tsx}"]}}